import React, { useState, useEffect, useCallback, useRef } from 'react';
import axios from 'axios';
import { useDrop } from 'react-dnd';
import { API_URL } from '../../config';
import { FileItem, ItemTypes } from './FileManagerTypes';
import ContextMenu from '../ContextMenu';
import { getAuthToken } from '../utils/storageUtils';

// Helper function to check if a folder is a descendant of another folder
const isFolderDescendant = (folderId: string, potentialAncestorId: string, folderHierarchy: FolderHierarchyItem[]): boolean => {
  // Build a map of parent-child relationships
  const childToParentMap = new Map<string, string>();

  // Recursive function to populate the map
  const populateMap = (folders: FolderHierarchyItem[], parentId: string | null = null) => {
    for (const folder of folders) {
      if (parentId) childToParentMap.set(folder.id, parentId);
      if (folder.children && folder.children.length > 0) {
        populateMap(folder.children, folder.id);
      }
    }
  };

  populateMap(folderHierarchy);

  // Check if folderId is a descendant of potentialAncestorId
  let currentId = folderId;
  while (currentId && childToParentMap.has(currentId)) {
    const parentId = childToParentMap.get(currentId);
    if (parentId === potentialAncestorId) return true;
    currentId = parentId;
  }

  return false;
};

// Define the folder hierarchy item type
interface FolderHierarchyItem {
  id: string;
  name: string;
  children: FolderHierarchyItem[];
  is_secure?: boolean;
}

// Define drag item type
interface DragItem {
  id: string;
  type: string;
}

// Define the component props
interface FileManagerFolderExplorerProps {
  farmId: string;
  currentFolder: string | null;
  onNavigate: (folderId: string | null, folderName: string) => void;
  onMove?: (itemIds: string[], targetFolderId: string | null) => void;
}

const FileManagerFolderExplorer: React.FC<FileManagerFolderExplorerProps> = ({
  farmId,
  currentFolder,
  onNavigate,
  onMove
}) => {
  // State
  const [folderHierarchy, setFolderHierarchy] = useState<FolderHierarchyItem[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  // Initialize with 'root' and 'farm-root' expanded to show folders under root by default
  const [expandedFolders, setExpandedFolders] = useState<Set<string>>(new Set(['root', 'farm-root']));

  // Fetch folder hierarchy
  const fetchFolderHierarchy = useCallback(async () => {
    setLoading(true);
    setError(null);

    try {
      const response = await axios.get(`${API_URL}/documents/farm/${farmId}/folder-hierarchy`, {
        headers: {
          Authorization: `Bearer ${getAuthToken()}`
        }
      });

      const folders = response.data;

      // Create a single "Farm name Root" folder that contains all root folders as children
      const farmRootFolder = {
        id: 'farm-root',
        name: 'Farm name Root',
        children: folders,
        is_secure: false
      };

      setFolderHierarchy([farmRootFolder]);

      // Expand all root-level folders by default
      const rootFolderIds = folders.map(folder => folder.id);
      setExpandedFolders(prev => {
        const newSet = new Set(prev);
        // Always ensure 'root' and 'farm-root' are in the set
        newSet.add('root');
        newSet.add('farm-root');
        // Add all root-level folder IDs
        rootFolderIds.forEach(id => newSet.add(id));
        return newSet;
      });
    } catch (err: any) {
      console.error('Error fetching folder hierarchy:', err);
      const errorMessage = err.response?.data?.error || 'Failed to load folder hierarchy. Please try again later.';
      setError(errorMessage);
    } finally {
      setLoading(false);
    }
  }, [farmId]);

  // Fetch folder hierarchy on mount and when farmId changes
  useEffect(() => {
    fetchFolderHierarchy();
  }, [fetchFolderHierarchy]);

  // Toggle folder expansion
  const toggleFolder = (folderId: string) => {
    setExpandedFolders(prev => {
      const newSet = new Set(prev);
      if (newSet.has(folderId)) {
        newSet.delete(folderId);
      } else {
        newSet.add(folderId);
      }
      return newSet;
    });
  };

  // Handle folder click
  const handleFolderClick = (folder: FolderHierarchyItem) => {
    // Just highlight the folder, don't navigate
    // This will be handled by the parent component
    // through the currentFolder prop
  };

  // Handle folder double click
  const handleFolderDoubleClick = (folder: FolderHierarchyItem, e?: React.MouseEvent) => {
    // Ensure the folder is expanded
    setExpandedFolders(prev => {
      const newSet = new Set(prev);
      newSet.add(folder.id);
      return newSet;
    });

    // Navigate to the folder
    // Special case for "Farm name Root" folder - navigate to null (root)
    if (folder.id === 'farm-root') {
      onNavigate(null, folder.name);
    } else {
      onNavigate(folder.id, folder.name);
    }

    // Prevent event bubbling
    e?.stopPropagation();
  };

  // FolderItem component to replace renderFolderItem function
  const FolderItem: React.FC<{
    folder: FolderHierarchyItem;
    level: number;
    expandedFolders: Set<string>;
    currentFolder: string | null;
    folderHierarchy: FolderHierarchyItem[];
    toggleFolder: (folderId: string) => void;
    handleFolderClick: (folder: FolderHierarchyItem) => void;
    handleFolderDoubleClick: (folder: FolderHierarchyItem, e?: React.MouseEvent) => void;
    onMove?: (itemIds: string[], targetFolderId: string | null) => void;
  }> = ({
    folder,
    level,
    expandedFolders,
    currentFolder,
    folderHierarchy,
    toggleFolder,
    handleFolderClick,
    handleFolderDoubleClick,
    onMove
  }) => {
    const isExpanded = expandedFolders.has(folder.id);
    const isSelected = currentFolder === folder.id;
    const hasChildren = folder.children && folder.children.length > 0;
    const ref = useRef<HTMLDivElement>(null);

    // Set up drop target for folder
    const [{ isOver, canDrop }, drop] = useDrop({
      accept: [ItemTypes.FILE, ItemTypes.FOLDER],
      canDrop: (draggedItem: DragItem) => {
        // Can't drop on itself
        if (draggedItem.id === folder.id) return false;
        // Can't drop if onMove is not provided
        if (!onMove) return false;
        // If it's a file being dragged, we can drop it anywhere
        if (draggedItem.type === ItemTypes.FILE) return true;
        // If it's a folder, we need to check if the target folder is a descendant of the dragged folder
        // to prevent circular references
        // Use the current folderHierarchy from component state
        return !isFolderDescendant(folder.id, draggedItem.id, folderHierarchy);
      },
      drop: (draggedItem: DragItem) => {
        if (onMove) {
          // Special case for "Farm name Root" folder - drop to null (root)
          if (folder.id === 'farm-root') {
            onMove([draggedItem.id], null);
          } else {
            onMove([draggedItem.id], folder.id);
          }
        }
        return { dropEffect: 'move' };
      },
      collect: (monitor) => ({
        isOver: monitor.isOver(),
        canDrop: monitor.canDrop(),
      }),
    });

    // Apply drop ref
    drop(ref);

    // Define context menu items for this folder
    const folderContextMenuItems = [
      {
        label: 'Open Folder',
        icon: (
          <svg className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M5 19a2 2 0 01-2-2V7a2 2 0 012-2h4l2 2h4a2 2 0 012 2v1M5 19h14a2 2 0 002-2v-5a2 2 0 00-2-2H9a2 2 0 00-2 2v5a2 2 0 01-2 2z"
            />
          </svg>
        ),
        onClick: () => handleFolderDoubleClick(folder, undefined)
      },
      {
        label: hasChildren ? (isExpanded ? 'Collapse' : 'Expand') : 'No Subfolders',
        icon: (
          <svg className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d={hasChildren
                ? (isExpanded
                  ? "M5 15l7-7 7 7"
                  : "M19 9l-7 7-7-7")
                : "M6 18L18 18M12 12v0"
              }
            />
          </svg>
        ),
        onClick: () => hasChildren && toggleFolder(folder.id),
        disabled: !hasChildren
      },
      {
        label: 'Create Subfolder',
        icon: (
          <svg className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M9 13h6m-3-3v6m-9-6h9m-9 6h9"
            />
          </svg>
        ),
        onClick: () => {
          // Dispatch a custom event to show the create folder modal
          const event = new CustomEvent('createFolderInParent', {
            detail: { parentFolderId: folder.id }
          });
          document.dispatchEvent(event);
        }
      }
    ];

    return (
      <ContextMenu items={folderContextMenuItems}>
        <div key={folder.id} className="folder-explorer-item">
          <div
            ref={ref}
            className={`folder-explorer-item-header ${isSelected ? 'selected' : ''} ${isOver && canDrop ? 'drop-target' : ''}`}
            style={{ paddingLeft: `${level * 16}px` }}
          >
            {hasChildren && (
              <button
                className="folder-explorer-toggle"
                onClick={() => toggleFolder(folder.id)}
              >
                {isExpanded ? '▼' : '►'}
              </button>
            )}
            <div
              className="folder-explorer-name"
              onClick={() => handleFolderClick(folder)}
              onDoubleClick={(e) => handleFolderDoubleClick(folder, e)}
            >
              <span className="folder-icon relative">
                📁
                {folder.is_secure && (
                  <span
                    className="absolute -top-1 -right-1 text-red-500"
                    title="Secure folder - cannot be shared with public link"
                  >
                    🔒
                  </span>
                )}
              </span>
              {folder.name}
            </div>
          </div>

          {isExpanded && hasChildren && (
            <div className="folder-explorer-children">
              {folder.children.map(child => (
                <FolderItem
                  key={child.id}
                  folder={child}
                  level={level + 1}
                  expandedFolders={expandedFolders}
                  currentFolder={currentFolder}
                  folderHierarchy={folderHierarchy}
                  toggleFolder={toggleFolder}
                  handleFolderClick={handleFolderClick}
                  handleFolderDoubleClick={handleFolderDoubleClick}
                  onMove={onMove}
                />
              ))}
            </div>
          )}
        </div>
      </ContextMenu>
    );
  };

  // Set up drop target for root folder (for dropping items directly to root)
  const rootRef = useRef<HTMLDivElement>(null);
  const [{ isOver: isRootOver, canDrop: canDropRoot }, dropRoot] = useDrop({
    accept: [ItemTypes.FILE, ItemTypes.FOLDER],
    canDrop: (draggedItem: DragItem) => {
      // Can't drop if onMove is not provided
      if (!onMove) return false;
      // Files can always be dropped in the root
      if (draggedItem.type === ItemTypes.FILE) return true;
      // For folders, no need to check for circular references when dropping in root
      // since root is always at the top of the hierarchy
      return true;
    },
    drop: (draggedItem: DragItem) => {
      if (onMove) {
        onMove([draggedItem.id], null);
      }
      return { dropEffect: 'move' };
    },
    collect: (monitor) => ({
      isOver: monitor.isOver(),
      canDrop: monitor.canDrop(),
    }),
  });

  // Apply drop ref to root folder
  dropRoot(rootRef);

  return (
    <div className="file-manager-folder-explorer">
      <div className="folder-explorer-header">
        <h3>Folders</h3>
        <button
          className="refresh-button"
          onClick={fetchFolderHierarchy}
          title="Refresh folder hierarchy"
        >
          🔄
        </button>
      </div>

      {loading ? (
        <div className="folder-explorer-loading">Loading folders...</div>
      ) : error ? (
        <div className="folder-explorer-error">{error}</div>
      ) : (
        <div className="folder-explorer-tree">
          {/* Hidden element for root drop target */}
          <div 
            ref={rootRef}
            className={`hidden-root-drop-target ${isRootOver && canDropRoot ? 'drop-target' : ''}`}
            style={{ height: '4px', marginBottom: '4px', backgroundColor: isRootOver && canDropRoot ? 'rgba(59, 130, 246, 0.5)' : 'transparent' }}
          />

          {folderHierarchy.map(folder => (
            <FolderItem
              key={folder.id}
              folder={folder}
              level={0}
              expandedFolders={expandedFolders}
              currentFolder={currentFolder}
              folderHierarchy={folderHierarchy}
              toggleFolder={toggleFolder}
              handleFolderClick={handleFolderClick}
              handleFolderDoubleClick={handleFolderDoubleClick}
              onMove={onMove}
            />
          ))}
        </div>
      )}
    </div>
  );
};

export default FileManagerFolderExplorer;
