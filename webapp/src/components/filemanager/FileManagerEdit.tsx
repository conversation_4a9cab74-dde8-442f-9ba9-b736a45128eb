import React, { useState, useEffect } from 'react';
import axios from 'axios';
import { API_URL } from '../../config';
import { FileItem } from './FileManagerTypes';
import LexicalEditor from '../LexicalEditor';
import { getAuthToken } from '../utils/storageUtils';

interface FileManagerEditProps {
  file: FileItem | null;
  onClose: () => void;
  onSave: (content: string) => Promise<void>;
  isOpen: boolean;
}

const FileManagerEdit: React.FC<FileManagerEditProps> = ({ file, onClose, onSave, isOpen }) => {
  const [content, setContent] = useState<string>('');
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [editorType, setEditorType] = useState<string>('');
  const [isSaving, setIsSaving] = useState<boolean>(false);

  useEffect(() => {
    if (!file || !isOpen) {
      setContent('');
      setEditorType('');
      return;
    }

    const fetchContent = async () => {
      setLoading(true);
      setError(null);

      try {
        // Determine editor type based on file mime type or extension
        const mimeType = file.mime_type || '';
        const fileExtension = file.file_type?.toLowerCase() || '';

        // Set editor type
        if (
          mimeType.startsWith('text/') || 
          ['txt', 'md', 'json', 'csv', 'xml', 'html', 'css', 'js', 'ts', 'jsx', 'tsx'].includes(fileExtension)
        ) {
          setEditorType('text');

          // For text files, fetch the content
          const response = await axios.get(`${API_URL}/api/documents/documents/${file.id}/content`, {
            headers: {
              Authorization: `Bearer ${getAuthToken()}`
            }
          });

          setContent(response.data.content || '');
        } else if (
          ['doc', 'docx'].includes(fileExtension) ||
          mimeType.includes('wordprocessingml')
        ) {
          setEditorType('word');
          // Word document editing will be implemented in future iterations
          setError('Word document editing is not yet supported. This feature is coming soon.');
        } else if (
          ['xls', 'xlsx'].includes(fileExtension) ||
          mimeType.includes('spreadsheetml')
        ) {
          setEditorType('spreadsheet');
          // Spreadsheet editing will be implemented in future iterations
          setError('Spreadsheet editing is not yet supported. This feature is coming soon.');
        } else if (mimeType === 'application/pdf' || fileExtension === 'pdf') {
          setEditorType('pdf');
          // PDF editing will be implemented in future iterations
          setError('PDF editing is not yet supported. This feature is coming soon.');
        } else {
          setEditorType('unsupported');
          setError('This file type is not supported for editing.');
        }
      } catch (err: any) {
        console.error('Error fetching content:', err);
        setError(err.response?.data?.error || 'Failed to load content for editing');
      } finally {
        setLoading(false);
      }
    };

    fetchContent();
  }, [file, isOpen]);

  const handleSave = async () => {
    if (!file) return;

    setIsSaving(true);
    setError(null);

    try {
      await onSave(content);
      // Close the editor after successful save
      onClose();
    } catch (err: any) {
      console.error('Error saving content:', err);
      setError(err.response?.data?.error || 'Failed to save content');
    } finally {
      setIsSaving(false);
    }
  };

  if (!isOpen || !file) {
    return null;
  }

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto">
      <div className="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
        <div className="fixed inset-0 transition-opacity" aria-hidden="true">
          <div className="absolute inset-0 bg-gray-500 opacity-75"></div>
        </div>

        <span className="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>

        <div className="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-5xl sm:w-full">
          <div className="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
            <div className="sm:flex sm:items-start">
              <div className="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left w-full">
                <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4">
                  Editing: {file.name}
                </h3>

                {loading ? (
                  <div className="flex justify-center items-center h-96">
                    <svg className="animate-spin h-10 w-10 text-primary-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                      <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                  </div>
                ) : error ? (
                  <div className="flex flex-col items-center justify-center h-96">
                    <svg className="h-16 w-16 text-red-500 mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                    </svg>
                    <p className="text-red-500">{error}</p>
                  </div>
                ) : (
                  <div className="editor-container h-96 overflow-auto">
                    {editorType === 'text' && (
                      <LexicalEditor
                        value={content}
                        onChange={setContent}
                        style={{ height: '100%', minHeight: '350px' }}
                      />
                    )}

                    {editorType === 'unsupported' && (
                      <div className="flex flex-col items-center justify-center h-full">
                        <svg className="h-16 w-16 text-gray-400 mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                        </svg>
                        <p className="text-gray-500 mb-4">This file type is not supported for editing</p>
                        <a 
                          href={`${API_URL}/api/documents/documents/${file.id}/download`}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
                        >
                          Download file
                        </a>
                      </div>
                    )}
                  </div>
                )}
              </div>
            </div>
          </div>
          <div className="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
            {editorType === 'text' && !error && (
              <button
                type="button"
                className="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-primary-600 text-base font-medium text-white hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 sm:ml-3 sm:w-auto sm:text-sm"
                onClick={handleSave}
                disabled={isSaving}
              >
                {isSaving ? 'Saving...' : 'Save'}
              </button>
            )}
            <button
              type="button"
              className="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm"
              onClick={onClose}
            >
              {editorType === 'text' && !error ? 'Cancel' : 'Close'}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default FileManagerEdit;