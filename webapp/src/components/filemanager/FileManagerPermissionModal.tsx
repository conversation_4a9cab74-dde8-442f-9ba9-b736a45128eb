import React, { useState, useEffect, useContext } from 'react';
import axios from 'axios';
import { API_URL } from '../../config';
import { AuthContext } from '../../context/AuthContext';
import { useFarm } from '../../context/FarmContext';
import { FileItem } from './FileManagerTypes';

interface Permission {
  id: string;
  entity_type: 'document' | 'folder';
  entity_id: string;
  user_id: string | null;
  role_id: string | null;
  farm_id: string;
  can_view: boolean;
  can_edit: boolean;
  can_delete: boolean;
  can_share: boolean;
  is_inherited: boolean;
  created_at: string;
  updated_at: string;
  user?: {
    id: string;
    first_name: string;
    last_name: string;
    email: string;
  };
  role?: {
    id: string;
    name: string;
    description: string;
  };
}

interface User {
  id: string;
  first_name: string;
  last_name: string;
  email: string;
}

interface Role {
  id: string;
  name: string;
  description: string;
}

interface FileManagerPermissionModalProps {
  isOpen: boolean;
  onClose: () => void;
  item: FileItem | null;
}

const FileManagerPermissionModal: React.FC<FileManagerPermissionModalProps> = ({
  isOpen,
  onClose,
  item
}) => {
  const [permissions, setPermissions] = useState<Permission[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [users, setUsers] = useState<User[]>([]);
  const [roles, setRoles] = useState<Role[]>([]);
  const [selectedUser, setSelectedUser] = useState<string>('');
  const [selectedRole, setSelectedRole] = useState<string>('');
  const [permissionType, setPermissionType] = useState<'user' | 'role'>('user');
  const [canView, setCanView] = useState(true);
  const [canEdit, setCanEdit] = useState(false);
  const [canDelete, setCanDelete] = useState(false);
  const [canShare, setCanShare] = useState(false);
  const [applyToChildren, setApplyToChildren] = useState(false);
  const [successMessage, setSuccessMessage] = useState<string | null>(null);

  const { user } = useContext(AuthContext);
  const { currentFarm } = useFarm();

  // Fetch permissions
  useEffect(() => {
    if (!isOpen || !item) return;

    const fetchPermissions = async () => {
      setLoading(true);
      setError(null);

      try {
        // Map 'file' type to 'document' for API compatibility
        const entityType = item.type === 'file' ? 'document' : item.type;
        const entityId = item.id;

        // Fetch permissions
        const permissionsResponse = await axios.get(`${API_URL}/documents/${entityType}/${entityId}/permissions`, {
          headers: {
            Authorization: `Bearer ${localStorage.getItem('token')}`
          }
        });

        setPermissions(permissionsResponse.data || []);

        // Fetch users
        const usersResponse = await axios.get(`${API_URL}/users/farm/${currentFarm?.id}`, {
          headers: {
            Authorization: `Bearer ${localStorage.getItem('token')}`
          }
        });

        setUsers(usersResponse.data || []);

        // Fetch roles
        const rolesResponse = await axios.get(`${API_URL}/roles/farm/${currentFarm?.id}`, {
          headers: {
            Authorization: `Bearer ${localStorage.getItem('token')}`
          }
        });

        setRoles(rolesResponse.data || []);
      } catch (err: any) {
        console.error('Error fetching permissions:', err);
        setError(err.response?.data?.error || 'Failed to load permissions. Please try again later.');
      } finally {
        setLoading(false);
      }
    };

    fetchPermissions();
  }, [isOpen, item, currentFarm?.id]);

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError(null);
    setSuccessMessage(null);

    if (!item) return;

    try {
      // Validate form
      if (permissionType === 'user' && !selectedUser) {
        setError('Please select a user');
        return;
      }

      if (permissionType === 'role' && !selectedRole) {
        setError('Please select a role');
        return;
      }

      // Map 'file' type to 'document' for API compatibility
      const entityType = item.type === 'file' ? 'document' : item.type;
      const entityId = item.id;

      // Prepare data
      const data = {
        userId: permissionType === 'user' ? selectedUser : undefined,
        roleId: permissionType === 'role' ? selectedRole : undefined,
        canView,
        canEdit,
        canDelete,
        canShare,
        applyToChildren: entityType === 'folder' ? applyToChildren : undefined
      };

      // Send request
      await axios.post(`${API_URL}/documents/${entityType}/${entityId}/permissions`, data, {
        headers: {
          Authorization: `Bearer ${localStorage.getItem('token')}`
        }
      });

      // Refresh permissions
      const permissionsResponse = await axios.get(`${API_URL}/documents/${entityType}/${entityId}/permissions`, {
        headers: {
          Authorization: `Bearer ${localStorage.getItem('token')}`
        }
      });

      setPermissions(permissionsResponse.data || []);
      setSuccessMessage('Permission set successfully');

      // Reset form
      setSelectedUser('');
      setSelectedRole('');
      setPermissionType('user');
      setCanView(true);
      setCanEdit(false);
      setCanDelete(false);
      setCanShare(false);
      setApplyToChildren(false);
    } catch (err: any) {
      console.error('Error setting permission:', err);
      setError(err.response?.data?.error || 'Failed to set permission. Please try again later.');
    }
  };

  // Handle permission deletion
  const handleDeletePermission = async (permissionId: string) => {
    if (!item) return;

    try {
      await axios.delete(`${API_URL}/documents/permissions/${permissionId}`, {
        headers: {
          Authorization: `Bearer ${localStorage.getItem('token')}`
        }
      });

      // Refresh permissions
      // Map 'file' type to 'document' for API compatibility
      const entityType = item.type === 'file' ? 'document' : item.type;
      const entityId = item.id;
      const permissionsResponse = await axios.get(`${API_URL}/documents/${entityType}/${entityId}/permissions`, {
        headers: {
          Authorization: `Bearer ${localStorage.getItem('token')}`
        }
      });

      setPermissions(permissionsResponse.data || []);
      setSuccessMessage('Permission deleted successfully');
    } catch (err: any) {
      console.error('Error deleting permission:', err);
      setError(err.response?.data?.error || 'Failed to delete permission. Please try again later.');
    }
  };

  // Format date for display
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString() + ' ' + date.toLocaleTimeString();
  };

  if (!isOpen || !item) {
    return null;
  }

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto">
      <div className="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
        <div className="fixed inset-0 transition-opacity" aria-hidden="true">
          <div className="absolute inset-0 bg-gray-500 opacity-75"></div>
        </div>

        <span className="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>

        <div className="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-5xl sm:w-full">
          <div className="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
            <div className="sm:flex sm:items-start">
              <div className="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left w-full">
                <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4">
                  Manage Permissions: {item.name}
                </h3>

                {/* Error message */}
                {error && (
                  <div className="mb-4 p-4 text-sm text-red-700 bg-red-100 rounded-md">
                    {error}
                  </div>
                )}

                {/* Success message */}
                {successMessage && (
                  <div className="mb-4 p-4 text-sm text-green-700 bg-green-100 rounded-md">
                    {successMessage}
                  </div>
                )}

                {/* Loading state */}
                {loading ? (
                  <div className="flex justify-center items-center h-64">
                    <svg
                      className="animate-spin h-8 w-8 text-primary-500"
                      xmlns="http://www.w3.org/2000/svg"
                      fill="none"
                      viewBox="0 0 24 24"
                    >
                      <circle
                        className="opacity-25"
                        cx="12"
                        cy="12"
                        r="10"
                        stroke="currentColor"
                        strokeWidth="4"
                      ></circle>
                      <path
                        className="opacity-75"
                        fill="currentColor"
                        d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                      ></path>
                    </svg>
                  </div>
                ) : (
                  <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                    {/* Current Permissions */}
                    <div>
                      <h2 className="text-lg font-medium text-gray-900 mb-4">Current Permissions</h2>
                      {permissions.length === 0 ? (
                        <p className="text-gray-500">No permissions set yet.</p>
                      ) : (
                        <div className="bg-white shadow overflow-hidden sm:rounded-md">
                          <ul className="divide-y divide-gray-200">
                            {permissions.map((permission) => (
                              <li key={permission.id} className="px-4 py-4">
                                <div className="flex items-center justify-between">
                                  <div>
                                    <p className="text-sm font-medium text-gray-900">
                                      {permission.user
                                        ? `User: ${permission.user.first_name} ${permission.user.last_name} (${permission.user.email})`
                                        : `Role: ${permission.role?.name}`}
                                    </p>
                                    <div className="mt-2 flex flex-wrap gap-2">
                                      {permission.can_view && (
                                        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                          View
                                        </span>
                                      )}
                                      {permission.can_edit && (
                                        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                          Edit
                                        </span>
                                      )}
                                      {permission.can_delete && (
                                        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                          Delete
                                        </span>
                                      )}
                                      {permission.can_share && (
                                        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-purple-100 text-purple-800">
                                          Share
                                        </span>
                                      )}
                                      {permission.is_inherited && (
                                        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                                          Inherited
                                        </span>
                                      )}
                                    </div>
                                    <p className="mt-1 text-xs text-gray-500">
                                      Created: {formatDate(permission.created_at)}
                                    </p>
                                  </div>
                                  {!permission.is_inherited && (
                                    <button
                                      onClick={() => handleDeletePermission(permission.id)}
                                      className="ml-2 inline-flex items-center p-1 border border-transparent rounded-full shadow-sm text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
                                      title="Delete permission"
                                    >
                                      <svg
                                        className="h-5 w-5"
                                        xmlns="http://www.w3.org/2000/svg"
                                        viewBox="0 0 20 20"
                                        fill="currentColor"
                                        aria-hidden="true"
                                      >
                                        <path
                                          fillRule="evenodd"
                                          d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z"
                                          clipRule="evenodd"
                                        />
                                      </svg>
                                    </button>
                                  )}
                                </div>
                              </li>
                            ))}
                          </ul>
                        </div>
                      )}
                    </div>

                    {/* Add Permission Form */}
                    <div>
                      <h2 className="text-lg font-medium text-gray-900 mb-4">Add Permission</h2>
                      <form onSubmit={handleSubmit} className="bg-white shadow sm:rounded-md p-6">
                        <div className="mb-4">
                          <label className="block text-sm font-medium text-gray-700 mb-2">
                            Permission Type
                          </label>
                          <div className="flex space-x-4">
                            <label className="inline-flex items-center">
                              <input
                                type="radio"
                                className="form-radio h-4 w-4 text-primary-600"
                                value="user"
                                checked={permissionType === 'user'}
                                onChange={() => setPermissionType('user')}
                              />
                              <span className="ml-2 text-sm text-gray-700">User</span>
                            </label>
                            <label className="inline-flex items-center">
                              <input
                                type="radio"
                                className="form-radio h-4 w-4 text-primary-600"
                                value="role"
                                checked={permissionType === 'role'}
                                onChange={() => setPermissionType('role')}
                              />
                              <span className="ml-2 text-sm text-gray-700">Role</span>
                            </label>
                          </div>
                        </div>

                        {permissionType === 'user' ? (
                          <div className="mb-4">
                            <label htmlFor="user" className="block text-sm font-medium text-gray-700 mb-2">
                              Select User
                            </label>
                            <select
                              id="user"
                              value={selectedUser}
                              onChange={(e) => setSelectedUser(e.target.value)}
                              className="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm rounded-md"
                            >
                              <option value="">Select a user</option>
                              {users.map((user) => (
                                <option key={user.id} value={user.id}>
                                  {user.first_name} {user.last_name} ({user.email})
                                </option>
                              ))}
                            </select>
                          </div>
                        ) : (
                          <div className="mb-4">
                            <label htmlFor="role" className="block text-sm font-medium text-gray-700 mb-2">
                              Select Role
                            </label>
                            <select
                              id="role"
                              value={selectedRole}
                              onChange={(e) => setSelectedRole(e.target.value)}
                              className="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm rounded-md"
                            >
                              <option value="">Select a role</option>
                              {roles.map((role) => (
                                <option key={role.id} value={role.id}>
                                  {role.name}
                                </option>
                              ))}
                            </select>
                          </div>
                        )}

                        <div className="mb-4">
                          <label className="block text-sm font-medium text-gray-700 mb-2">
                            Permissions
                          </label>
                          <div className="space-y-2">
                            <label className="inline-flex items-center">
                              <input
                                type="checkbox"
                                className="form-checkbox h-4 w-4 text-primary-600"
                                checked={canView}
                                onChange={(e) => setCanView(e.target.checked)}
                              />
                              <span className="ml-2 text-sm text-gray-700">View</span>
                            </label>
                            <label className="inline-flex items-center">
                              <input
                                type="checkbox"
                                className="form-checkbox h-4 w-4 text-primary-600"
                                checked={canEdit}
                                onChange={(e) => setCanEdit(e.target.checked)}
                              />
                              <span className="ml-2 text-sm text-gray-700">Edit</span>
                            </label>
                            <label className="inline-flex items-center">
                              <input
                                type="checkbox"
                                className="form-checkbox h-4 w-4 text-primary-600"
                                checked={canDelete}
                                onChange={(e) => setCanDelete(e.target.checked)}
                              />
                              <span className="ml-2 text-sm text-gray-700">Delete</span>
                            </label>
                            <label className="inline-flex items-center">
                              <input
                                type="checkbox"
                                className="form-checkbox h-4 w-4 text-primary-600"
                                checked={canShare}
                                onChange={(e) => setCanShare(e.target.checked)}
                              />
                              <span className="ml-2 text-sm text-gray-700">Share</span>
                            </label>
                          </div>
                        </div>

                        {item.type === 'folder' && (
                          <div className="mb-4">
                            <label className="inline-flex items-center">
                              <input
                                type="checkbox"
                                className="form-checkbox h-4 w-4 text-primary-600"
                                checked={applyToChildren}
                                onChange={(e) => setApplyToChildren(e.target.checked)}
                              />
                              <span className="ml-2 text-sm text-gray-700">
                                Apply to all files and subfolders
                              </span>
                            </label>
                          </div>
                        )}

                        <div className="mt-6">
                          <button
                            type="submit"
                            className="w-full inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
                          >
                            Add Permission
                          </button>
                        </div>
                      </form>
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>
          <div className="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
            <button
              type="button"
              className="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm"
              onClick={onClose}
            >
              Close
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default FileManagerPermissionModal;
