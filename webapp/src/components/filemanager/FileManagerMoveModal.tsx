import React, { useState, useEffect, useCallback } from 'react';
import axios from 'axios';
import { API_URL } from '../../config';
import { FileItem, BreadcrumbItem } from './FileManagerTypes';
import { getAuthToken } from '../utils/storageUtils';

// Helper function to check if a folder is a descendant of any of the items being moved
const isFolderDescendantOfItems = (folder: FileItem, items: FileItem[], folderHierarchy: FileItem[]): boolean => {
  // If we're moving files only, no need to check for circular references
  const movingFolders = items.filter(item => item.type === 'folder');
  if (movingFolders.length === 0) return false;

  // Check if the folder is one of the items being moved
  if (items.some(item => item.id === folder.id)) return true;

  // Build a map of parent-child relationships from the folder hierarchy
  const childToParentMap = new Map<string, string>();

  // Recursive function to populate the map
  const populateMap = (folders: FileItem[], parentId: string | null = null) => {
    for (const f of folders) {
      if (parentId) childToParentMap.set(f.id, parentId);
      if (f.children && f.children.length > 0) {
        populateMap(f.children, f.id);
      }
    }
  };

  populateMap(folderHierarchy);

  // Check if the folder is a descendant of any of the items being moved
  for (const item of movingFolders) {
    let currentFolderId = folder.id;
    while (currentFolderId && childToParentMap.has(currentFolderId)) {
      const parentId = childToParentMap.get(currentFolderId);
      if (parentId === item.id) return true;
      currentFolderId = parentId;
    }
  }

  return false;
};

interface FileManagerMoveModalProps {
  isOpen: boolean;
  onClose: () => void;
  items: FileItem[];
  onMove: (itemIds: string[], targetFolderId: string | null) => Promise<void>;
  currentFolder: string | null;
  farmId: string;
}

const FileManagerMoveModal: React.FC<FileManagerMoveModalProps> = ({
  isOpen,
  onClose,
  items,
  onMove,
  currentFolder,
  farmId
}) => {
  const [folders, setFolders] = useState<FileItem[]>([]);
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [selectedFolderId, setSelectedFolderId] = useState<string | null>(null);
  const [breadcrumbs, setBreadcrumbs] = useState<BreadcrumbItem[]>([
    { id: null, name: 'Root' }
  ]);
  const [folderHierarchy, setFolderHierarchy] = useState<FileItem[]>([]);

  // Fetch folders
  const fetchFolders = useCallback(async (folderId: string | null) => {
    setLoading(true);
    setError(null);

    try {
      let url = `${API_URL}/documents/farm/${farmId}/folders`;
      if (folderId) {
        url += `?parentFolderId=${folderId}`;
      } else {
        url += `?parentFolderId=null`;
      }

      const response = await axios.get(url, {
        headers: {
          Authorization: `Bearer ${getAuthToken()}`
        }
      });

      // Filter out folders that are being moved or are descendants of folders being moved
      // This prevents moving a folder into itself or any of its descendants (which would create a circular reference)
      const filteredFolders = response.data.filter((folder: FileItem) => {
        // Check if this folder is one of the items being moved
        const isBeingMoved = items.some(item => item.id === folder.id);
        if (isBeingMoved) return false;

        // Check if this folder is a descendant of any folder being moved
        const isDescendant = isFolderDescendantOfItems(folder, items, folderHierarchy);
        return !isDescendant;
      });

      setFolders(filteredFolders);
      setSelectedFolderId(folderId);
    } catch (err: any) {
      console.error('Error fetching folders:', err);
      setError(err.response?.data?.error || 'Failed to load folders');
    } finally {
      setLoading(false);
    }
  }, [farmId, items, folderHierarchy]);

  // Fetch folder hierarchy
  const fetchFolderHierarchy = useCallback(async () => {
    try {
      const response = await axios.get(`${API_URL}/documents/farm/${farmId}/folder-hierarchy`, {
        headers: {
          Authorization: `Bearer ${getAuthToken()}`
        }
      });

      setFolderHierarchy(response.data || []);
    } catch (err: any) {
      console.error('Error fetching folder hierarchy:', err);
      setError(err.response?.data?.error || 'Failed to load folder hierarchy');
    }
  }, [farmId]);

  // Initialize
  useEffect(() => {
    if (isOpen) {
      // Fetch folder hierarchy first, then fetch folders
      fetchFolderHierarchy().then(() => {
        fetchFolders(null);
        setBreadcrumbs([{ id: null, name: 'Root' }]);
      });
    }
  }, [isOpen, fetchFolders, fetchFolderHierarchy]);

  // Handle folder navigation
  const handleFolderClick = useCallback((folder: FileItem) => {
    fetchFolders(folder.id);
    setBreadcrumbs(prev => [...prev, { id: folder.id, name: folder.name }]);
  }, [fetchFolders]);

  // Handle breadcrumb navigation
  const handleBreadcrumbClick = useCallback((breadcrumb: BreadcrumbItem) => {
    fetchFolders(breadcrumb.id);
    const index = breadcrumbs.findIndex(item => item.id === breadcrumb.id);
    if (index >= 0) {
      setBreadcrumbs(breadcrumbs.slice(0, index + 1));
    }
  }, [breadcrumbs, fetchFolders]);

  // Handle move
  const handleMoveClick = useCallback(async () => {
    try {
      setLoading(true);
      await onMove(items.map(item => item.id), selectedFolderId);
      onClose();
    } catch (err) {
      console.error('Error moving items:', err);
      setError('Failed to move items');
    } finally {
      setLoading(false);
    }
  }, [items, selectedFolderId, onMove, onClose]);

  if (!isOpen) {
    return null;
  }

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto">
      <div className="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
        <div className="fixed inset-0 transition-opacity" aria-hidden="true">
          <div className="absolute inset-0 bg-gray-500 opacity-75"></div>
        </div>

        <span className="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>

        <div className="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
          <div className="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
            <div className="sm:flex sm:items-start">
              <div className="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left w-full">
                <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4">
                  Move {items.length} {items.length === 1 ? 'item' : 'items'}
                </h3>

                {error && (
                  <div className="mb-4 p-2 text-sm text-red-700 bg-red-100 rounded-md">
                    {error}
                  </div>
                )}

                {/* Breadcrumbs */}
                <div className="flex items-center flex-wrap mb-4">
                  {breadcrumbs.map((breadcrumb, index) => (
                    <React.Fragment key={breadcrumb.id || 'root'}>
                      {index > 0 && (
                        <svg className="h-4 w-4 text-gray-400 mx-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                        </svg>
                      )}
                      <button
                        onClick={() => handleBreadcrumbClick(breadcrumb)}
                        className={`text-sm ${
                          index === breadcrumbs.length - 1
                            ? 'font-semibold text-primary-600'
                            : 'text-gray-600 hover:text-primary-600'
                        }`}
                      >
                        {breadcrumb.name}
                      </button>
                    </React.Fragment>
                  ))}
                </div>

                {/* Folder list */}
                <div className="border rounded-md overflow-auto max-h-60">
                  {loading ? (
                    <div className="flex justify-center items-center h-32">
                      <svg className="animate-spin h-6 w-6 text-primary-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                        <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                      </svg>
                    </div>
                  ) : folders.length === 0 ? (
                    <div className="flex justify-center items-center h-32 text-gray-500">
                      No folders found
                    </div>
                  ) : (
                    <ul className="divide-y divide-gray-200">
                      {folders.map(folder => (
                        <li key={folder.id} className="p-3 hover:bg-gray-50 cursor-pointer" onClick={() => handleFolderClick(folder)}>
                          <div className="flex items-center">
                            <svg className="h-5 w-5 text-yellow-500 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-6l-2-2H5a2 2 0 00-2 2z" />
                            </svg>
                            <span className="text-sm font-medium text-gray-900">{folder.name}</span>
                          </div>
                        </li>
                      ))}
                    </ul>
                  )}
                </div>

                <div className="mt-4">
                  <p className="text-sm text-gray-500">
                    Selected destination: <span className="font-medium">{breadcrumbs[breadcrumbs.length - 1].name}</span>
                  </p>
                </div>
              </div>
            </div>
          </div>
          <div className="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
            <button
              type="button"
              className="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-primary-600 text-base font-medium text-white hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 sm:ml-3 sm:w-auto sm:text-sm"
              onClick={handleMoveClick}
              disabled={loading}
            >
              Move Here
            </button>
            <button
              type="button"
              className="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm"
              onClick={onClose}
              disabled={loading}
            >
              Cancel
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default FileManagerMoveModal;
