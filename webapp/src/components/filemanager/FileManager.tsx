import React, { useState, useEffect, useCallback, useContext, useRef } from 'react';
import { DndProvider } from 'react-dnd';
import { HTML5Backend } from 'react-dnd-html5-backend';
import axios from 'axios';
import { useDropzone } from 'react-dropzone';
import { AuthContext } from '../../context/AuthContext';
import { useNotification } from '../../context/NotificationContext';
import { API_URL } from '../../config';
import { getAuthToken } from '../../utils/storageUtils';
import {
  FileItem,
  BreadcrumbItem,
  ViewMode,
  FileManagerProps
} from './FileManagerTypes';
import ContextMenu from '../ContextMenu';
import FileManagerToolbar from './FileManagerToolbar';
import FileManagerBreadcrumb from './FileManagerBreadcrumb';
import FileManagerGrid from './FileManagerGrid';
import FileManagerList from './FileManagerList';
import FileManagerUploadModal from './FileManagerUploadModal';
import FileManagerCreateFolderModal from './FileManagerCreateFolderModal';
import FileManagerCreateDocumentModal from './FileManagerCreateDocumentModal';
import FileManagerRenameModal from './FileManagerRenameModal';
import FileManagerDeleteModal from './FileManagerDeleteModal';
import FileManagerPreview from './FileManagerPreview';
import FileManagerMoveModal from './FileManagerMoveModal';
import FileManagerPermissionModal from './FileManagerPermissionModal';
import FileManagerFolderExplorer from './FileManagerFolderExplorer';
import './FileManagerStyles.css';
import FileManagerShareModal from './FileManagerShareModal';

const FileManager: React.FC<FileManagerProps> = ({
  farmId,
  onFileOpen,
  onFolderOpen,
  initialFolderId = null,
  onFolderNavigation,
  className = ''
}) => {
  console.log('FileManager: Component mounted/re-rendered', { farmId, initialFolderId });
  // State
  const [items, setItems] = useState<FileItem[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [currentFolder, setCurrentFolder] = useState<string | null>(null);
  const [breadcrumbs, setBreadcrumbs] = useState<BreadcrumbItem[]>([
    { id: null, name: 'Farm name Root' }
  ]);
  const [searchQuery, setSearchQuery] = useState('');
  const [viewMode, setViewMode] = useState<ViewMode>(() => {
    // Initialize from localStorage if available, otherwise default to 'grid'
    const savedViewMode = localStorage.getItem('fileManagerViewMode');
    return (savedViewMode === 'list' || savedViewMode === 'grid') ? savedViewMode : 'grid';
  });
  const [selectedItems, setSelectedItems] = useState<string[]>([]);
  const [sortBy, setSortBy] = useState<string>('name');
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('asc');
  const [focusedItemIndex, setFocusedItemIndex] = useState<number>(-1);
  const [isDragActive, setIsDragActive] = useState(false);

  // Storage quota state
  const [quotaInfo, setQuotaInfo] = useState<{
    currentUsage: number;
    quota: number;
    usagePercentage: number;
    documentCount: number;
    externalDocumentCount: number;
    quotaGB: number;
    usageGB: number;
    lastCalculatedAt: string;
  } | null>(null);
  const [quotaLoading, setQuotaLoading] = useState(false);

  // Drag selection state
  const [isDragSelecting, setIsDragSelecting] = useState(false);
  const [dragStartPosition, setDragStartPosition] = useState<{ x: number, y: number } | null>(null);
  const [dragCurrentPosition, setDragCurrentPosition] = useState<{ x: number, y: number } | null>(null);
  const [itemRefs, setItemRefs] = useState<Map<string, HTMLElement>>(new Map());

  // Clipboard state for copy, cut, paste operations
  const [clipboardItems, setClipboardItems] = useState<FileItem[]>([]);
  const [clipboardOperation, setClipboardOperation] = useState<'copy' | 'cut' | null>(null);

  // Modals
  const [uploadModalOpen, setUploadModalOpen] = useState(false);
  const [createFolderModalOpen, setCreateFolderModalOpen] = useState(false);
  const [createDocumentModalOpen, setCreateDocumentModalOpen] = useState(false);
  const [renameModalOpen, setRenameModalOpen] = useState(false);
  const [deleteModalOpen, setDeleteModalOpen] = useState(false);
  const [previewModalOpen, setPreviewModalOpen] = useState(false);
  const [moveModalOpen, setMoveModalOpen] = useState(false);
  const [permissionModalOpen, setPermissionModalOpen] = useState(false);
  const [shareModalOpen, setShareModalOpen] = useState(false);
  const [itemToRename, setItemToRename] = useState<FileItem | null>(null);
  const [itemsToDelete, setItemsToDelete] = useState<FileItem[]>([]);
  const [itemToPreview, setItemToPreview] = useState<FileItem | null>(null);
  const [itemsToMove, setItemsToMove] = useState<FileItem[]>([]);
  const [itemToManagePermissions, setItemToManagePermissions] = useState<FileItem | null>(null);
  const [itemToShare, setItemToShare] = useState<FileItem | null>(null);

  // Context
  const { user } = useContext(AuthContext);
  const { showNotification } = useNotification();

  // Cache for API responses
  const cache = useRef<{
    [key: string]: {
      files: FileItem[];
      folders: FileItem[];
      timestamp: number;
    }
  }>({});

  // Invalidate cache for current folder
  const invalidateCache = useCallback(() => {
    // Clear all cache entries related to the current folder
    const prefix = `${farmId || user?.farm_id}_${currentFolder || 'root'}`;
    Object.keys(cache.current).forEach(key => {
      if (key.startsWith(prefix)) {
        delete cache.current[key];
      }
    });
  }, [farmId, user?.farm_id, currentFolder]);

  // Fetch storage quota information
  const fetchQuotaInfo = useCallback(async () => {
    const effectiveFarmId = farmId || user?.farm_id;
    if (!effectiveFarmId) {
      console.warn('FileManager: fetchQuotaInfo called without valid farm ID');
      return;
    }

    setQuotaLoading(true);
    try {
      const response = await axios.get(`${API_URL}/api/documents/farm/${effectiveFarmId}/storage-quota`, {
        headers: {
          Authorization: `Bearer ${getAuthToken()}`
        }
      });

      setQuotaInfo(response.data);
    } catch (error) {
      console.error('Error fetching storage quota information:', error);
      // Don't show an error notification as this is not critical for the file manager to function
    } finally {
      setQuotaLoading(false);
    }
  }, [farmId, user?.farm_id]);

  // Fetch files and folders
  const fetchItems = useCallback(async () => {
    console.log('FileManager: fetchItems called', { farmId, userFarmId: user?.farm_id, currentFolder });

    const effectiveFarmId = farmId || user?.farm_id;
    if (!effectiveFarmId) {
      console.warn('FileManager: fetchItems called without valid farm ID');
      setError('No farm selected. Please select a farm to view files.');
      setLoading(false);
      return;
    }

    setLoading(true);
    setError(null);

    try {
      // Create cache key based on current parameters
      const cacheKey = `${farmId || user?.farm_id}_${currentFolder || 'root'}_${searchQuery}`;

      // Check if we have a valid cached response (less than 30 seconds old)
      const now = Date.now();
      const cachedData = cache.current[cacheKey];
      const isCacheValid = cachedData && (now - cachedData.timestamp < 30000) && !searchQuery;

      let files: FileItem[] = [];
      let folders: FileItem[] = [];

      if (isCacheValid) {
        // Use cached data
        console.log('Using cached data for', cacheKey);
        files = cachedData.files;
        folders = cachedData.folders;
      } else {
        // Fetch files
        let filesUrl = `${API_URL}/api/documents/farm/${farmId || user?.farm_id}/documents`;
        const fileParams: Record<string, string> = {};

        if (currentFolder) {
          fileParams.folderId = currentFolder;
        } else if (currentFolder === null) {
          fileParams.folderId = 'null'; // Explicitly request root files
        }

        if (searchQuery) {
          fileParams.search = searchQuery;
        }

        // Add query parameters
        if (Object.keys(fileParams).length > 0) {
          const queryString = new URLSearchParams(fileParams).toString();
          filesUrl = `${filesUrl}?${queryString}`;
        }

        const filesResponse = await axios.get(filesUrl, {
          headers: {
            Authorization: `Bearer ${localStorage.getItem('token')}`
          }
        });

        files = (filesResponse.data.documents || []).map((doc: any) => ({
          ...doc,
          type: 'file'
        }));

        // Fetch folders
        let foldersUrl = `${API_URL}/api/documents/farm/${farmId || user?.farm_id}/folders`;
        const folderParams: Record<string, string> = {};

        if (currentFolder) {
          folderParams.parentFolderId = currentFolder;
        } else if (currentFolder === null) {
          folderParams.parentFolderId = 'null'; // Explicitly request root folders
        }

        if (searchQuery) {
          folderParams.search = searchQuery;
        }

        // Add query parameters
        if (Object.keys(folderParams).length > 0) {
          const queryString = new URLSearchParams(folderParams).toString();
          foldersUrl = `${foldersUrl}?${queryString}`;
        }

        const foldersResponse = await axios.get(foldersUrl, {
          headers: {
            Authorization: `Bearer ${getAuthToken()}`
          }
        });

        folders = (foldersResponse.data || []).map((folder: any) => ({
          ...folder,
          type: 'folder'
        }));

        // Update cache if not a search query
        if (!searchQuery) {
          cache.current[cacheKey] = {
            files,
            folders,
            timestamp: now
          };
        }
      }

      // Combine and sort items
      const allItems = [...folders, ...files];
      const sortedItems = sortItems(allItems, sortBy, sortDirection);

      setItems(sortedItems);
    } catch (err: any) {
      console.error('Error fetching items:', err);
      const errorMessage = err.response?.data?.error || 'Failed to load files and folders. Please try again later.';
      setError(errorMessage);
    } finally {
      setLoading(false);
    }
  }, [farmId, user?.farm_id, currentFolder, searchQuery, sortBy, sortDirection]);

  // Sort items
  const sortItems = (items: FileItem[], sortBy: string, direction: 'asc' | 'desc') => {
    return [...items].sort((a, b) => {
      // Always put folders before files
      if (a.type === 'folder' && b.type === 'file') return -1;
      if (a.type === 'file' && b.type === 'folder') return 1;

      // Then sort by the specified field
      let valueA, valueB;

      switch (sortBy) {
        case 'name':
          valueA = a.name.toLowerCase();
          valueB = b.name.toLowerCase();
          break;
        case 'size':
          valueA = a.file_size || 0;
          valueB = b.file_size || 0;
          break;
        case 'date':
          valueA = new Date(a.created_at).getTime();
          valueB = new Date(b.created_at).getTime();
          break;
        case 'type':
          valueA = a.file_type || '';
          valueB = b.file_type || '';
          break;
        default:
          valueA = a.name.toLowerCase();
          valueB = b.name.toLowerCase();
      }

      // Apply sort direction
      if (direction === 'asc') {
        return valueA < valueB ? -1 : valueA > valueB ? 1 : 0;
      } else {
        return valueA > valueB ? -1 : valueA < valueB ? 1 : 0;
      }
    });
  };

  // Initial fetch
  useEffect(() => {
    console.log('FileManager: Initial fetch effect triggered', {
      farmId,
      userFarmId: user?.farm_id,
      loading
    });

    if (farmId || user?.farm_id) {
      try {
        fetchItems();
        fetchQuotaInfo();
      } catch (error) {
        console.error('FileManager: Error in initial fetch:', error);
        setError('Failed to initialize file manager. Please try refreshing the page.');
        setLoading(false);
      }
    } else {
      console.warn('FileManager: No farm ID available for initial fetch');
      setError('No farm selected. Please select a farm to view files.');
      setLoading(false);
    }
  }, [farmId, user?.farm_id, fetchItems, fetchQuotaInfo]);

  // Set initial folder from URL if provided, but only after the file manager is loaded
  useEffect(() => {
    // Run this effect when initialFolderId changes, even if it's the same as currentFolder
    // This ensures that clicking breadcrumbs works correctly
    // Only proceed if initialFolderId is defined and the file manager is not in a loading state
    if (initialFolderId !== undefined && !loading) {
      // Set the current folder after the file manager is loaded
      // This ensures we don't have a race condition where folder navigation happens before file manager is ready
      setCurrentFolder(initialFolderId);

      // We need to fetch the folder details to get the name for breadcrumbs
      const fetchFolderDetails = async () => {
        try {
          // If initialFolderId is null, we're navigating to root
          if (initialFolderId === null) {
            setBreadcrumbs([{ id: null, name: 'Farm name Root' }]);
            return;
          }

          // Try to get folder name from localStorage first
          const storedFolderName = localStorage.getItem(`folderName_${farmId || user?.farm_id}_${initialFolderId}`);

          if (storedFolderName) {
            // If we have the folder name in localStorage, use it
            setBreadcrumbs(prev => {
              // Check if we're navigating to a folder that's already in the breadcrumbs
              const existingIndex = prev.findIndex(item => item.id === initialFolderId);
              if (existingIndex >= 0) {
                // If so, truncate the breadcrumbs to that point
                return prev.slice(0, existingIndex + 1);
              }
              // Otherwise, add the new folder to the breadcrumbs
              return [...prev, { id: initialFolderId, name: storedFolderName }];
            });
          } else {
            // If not in localStorage, fetch from API
            const response = await axios.get(`${API_URL}/api/documents/folders/${initialFolderId}`, {
              headers: {
                Authorization: `Bearer ${getAuthToken()}`
              }
            });

            if (response.data) {
              // Update breadcrumbs with the folder name from API
              setBreadcrumbs(prev => {
                // Check if we're navigating to a folder that's already in the breadcrumbs
                const existingIndex = prev.findIndex(item => item.id === initialFolderId);
                if (existingIndex >= 0) {
                  // If so, truncate the breadcrumbs to that point
                  return prev.slice(0, existingIndex + 1);
                }
                // Otherwise, add the new folder to the breadcrumbs
                return [...prev, { id: initialFolderId, name: response.data.name }];
              });

              // Store folder name in localStorage for future use
              localStorage.setItem(`folderName_${farmId || user?.farm_id}_${initialFolderId}`, response.data.name);
            }
          }
        } catch (err) {
          console.error('Error fetching folder details:', err);
          // If we can't fetch the folder details, try to get from localStorage
          const storedFolderName = localStorage.getItem(`folderName_${farmId || user?.farm_id}_${initialFolderId}`);

          // Use stored name if available, otherwise use 'Folder'
          setBreadcrumbs(prev => [...prev, {
            id: initialFolderId,
            name: storedFolderName || 'Folder'
          }]);
        }
      };

      fetchFolderDetails();
    }
  }, [initialFolderId, loading, API_URL, farmId, user?.farm_id]);

  // Handle folder navigation
  const navigateToFolder = useCallback((folderId: string | null, folderName: string) => {
    setCurrentFolder(folderId);
    setSelectedItems([]);

    if (folderId === null) {
      // Reset to root
      setBreadcrumbs([{ id: null, name: 'Farm name Root' }]);
      // Clear folder name from localStorage
      localStorage.removeItem(`folderName_${farmId || user?.farm_id}_${folderId}`);
    } else {
      // Add to breadcrumbs
      setBreadcrumbs(prev => {
        // Check if we're navigating to a folder that's already in the breadcrumbs
        const existingIndex = prev.findIndex(item => item.id === folderId);
        if (existingIndex >= 0) {
          // If so, truncate the breadcrumbs to that point
          return prev.slice(0, existingIndex + 1);
        }
        // Otherwise, add the new folder to the breadcrumbs
        return [...prev, { id: folderId, name: folderName }];
      });

      // Store folder name in localStorage for persistence across refreshes
      localStorage.setItem(`folderName_${farmId || user?.farm_id}_${folderId}`, folderName);
    }

    // Update URL via the callback if provided
    if (onFolderNavigation) {
      onFolderNavigation(folderId, folderName);
    }
  }, [onFolderNavigation, farmId, user?.farm_id]);

  // Register item ref for drag selection
  const registerItemRef = useCallback((itemId: string, element: HTMLElement | null) => {
    if (element) {
      setItemRefs(prev => {
        const newMap = new Map(prev);
        newMap.set(itemId, element);
        return newMap;
      });
    }
  }, []);

  // Check if an item is within the drag selection area
  const isItemInSelectionArea = useCallback((itemId: string) => {
    if (!isDragSelecting || !dragStartPosition || !dragCurrentPosition || !itemRefs.has(itemId)) {
      return false;
    }

    const itemElement = itemRefs.get(itemId);
    if (!itemElement) return false;

    const itemRect = itemElement.getBoundingClientRect();

    // Calculate selection rectangle
    const selectionRect = {
      left: Math.min(dragStartPosition.x, dragCurrentPosition.x),
      top: Math.min(dragStartPosition.y, dragCurrentPosition.y),
      right: Math.max(dragStartPosition.x, dragCurrentPosition.x),
      bottom: Math.max(dragStartPosition.y, dragCurrentPosition.y)
    };

    // Check if the item intersects with the selection rectangle
    return !(
      itemRect.right < selectionRect.left ||
      itemRect.left > selectionRect.right ||
      itemRect.bottom < selectionRect.top ||
      itemRect.top > selectionRect.bottom
    );
  }, [isDragSelecting, dragStartPosition, dragCurrentPosition, itemRefs]);

  // Handle item selection
  const handleItemSelect = useCallback((itemId: string, isMultiSelect: boolean, isShiftSelect: boolean = false) => {
    setSelectedItems(prev => {
      if (isShiftSelect && focusedItemIndex >= 0) {
        // Shift key selection - select a range of items
        const itemIds = items.map(item => item.id);
        const currentIndex = itemIds.indexOf(itemId);
        const startIndex = Math.min(focusedItemIndex, currentIndex);
        const endIndex = Math.max(focusedItemIndex, currentIndex);

        // Get all items in the range
        const rangeIds = itemIds.slice(startIndex, endIndex + 1);

        // If multi-select (Ctrl+Shift), add to existing selection
        if (isMultiSelect) {
          const newSelection = new Set([...prev, ...rangeIds]);
          return Array.from(newSelection);
        } else {
          // Otherwise, replace selection with the range
          return rangeIds;
        }
      } else if (isMultiSelect) {
        // Toggle selection with multi-select (Ctrl/Cmd key)
        return prev.includes(itemId)
          ? prev.filter(id => id !== itemId)
          : [...prev, itemId];
      } else {
        // Single selection
        return prev.includes(itemId) && prev.length === 1
          ? [] // Deselect if already selected
          : [itemId]; // Select only this item
      }
    });

    // Update focused item index
    const itemIds = items.map(item => item.id);
    const newFocusedIndex = itemIds.indexOf(itemId);
    if (newFocusedIndex >= 0) {
      setFocusedItemIndex(newFocusedIndex);
    }
  }, [items, focusedItemIndex]);

  // Handle item double click
  const handleItemDoubleClick = useCallback((item: FileItem) => {
    if (item.type === 'folder') {
      navigateToFolder(item.id, item.name);
      if (onFolderOpen) onFolderOpen(item);
    } else {
      // Check if the file type is supported for direct opening
      const mimeType = item.mime_type || '';
      const fileExtension = item.file_type?.toLowerCase() || '';

      const isDirectlyOpenable = 
        mimeType.startsWith('image/') || 
        mimeType === 'application/pdf' || 
        mimeType.startsWith('video/') ||
        mimeType.startsWith('audio/') ||
        ['pdf', 'jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp', 'mp4', 'webm', 'ogg', 'mp3', 'wav'].includes(fileExtension);

      if (isDirectlyOpenable) {
        // Open the file directly in a new tab
        window.open(`${API_URL}/api/documents/documents/${item.id}/download`, '_blank');
      } else {
        // Open preview for files that can't be directly opened
        setItemToPreview(item);
        setPreviewModalOpen(true);
      }

      // Still call onFileOpen if provided (for compatibility)
      if (onFileOpen) onFileOpen(item);
    }
  }, [navigateToFolder, onFileOpen, onFolderOpen]);

  // Handle search
  const handleSearch = useCallback((query: string) => {
    setSearchQuery(query);
  }, []);

  // Handle view mode change
  const handleViewModeChange = useCallback((mode: ViewMode) => {
    // Save to localStorage to persist the user's preference
    localStorage.setItem('fileManagerViewMode', mode);
    setViewMode(mode);
  }, []);

  // Handle sort change
  const handleSortChange = useCallback((field: string) => {
    setSortBy(field);
    setSortDirection(prev => (sortBy === field ? (prev === 'asc' ? 'desc' : 'asc') : 'asc'));
  }, [sortBy]);

  // Handle file upload
  const handleUpload = useCallback(async (files: File[], folderId: string | null) => {
    try {
      setLoading(true);

      const formData = new FormData();
      files.forEach(file => {
        formData.append('files', file);
      });

      if (folderId) {
        formData.append('folderId', folderId);
      }

      await axios.post(
        `${API_URL}/api/documents/farm/${farmId || user?.farm_id}/upload`,
        formData,
        {
          headers: {
            'Content-Type': 'multipart/form-data',
            Authorization: `Bearer ${localStorage.getItem('token')}`
          }
        }
      );

      // Invalidate cache before refreshing
      invalidateCache();

      // Refresh items
      await fetchItems();

      // Update quota information after upload
      fetchQuotaInfo();

      setUploadModalOpen(false);
    } catch (err: any) {
      console.error('Error uploading files:', err);
      setError(err.response?.data?.error || 'Failed to upload files. Please try again later.');
    } finally {
      setLoading(false);
    }
  }, [farmId, user?.farm_id, fetchItems, invalidateCache, fetchQuotaInfo]);

  // Handle file drop for direct uploads
  const onDrop = useCallback((acceptedFiles: File[]) => {
    if (acceptedFiles.length > 0) {
      handleUpload(acceptedFiles, currentFolder);
    }
  }, [handleUpload, currentFolder]);

  // Set up dropzone for the entire file manager
  const { getRootProps, getInputProps, isDragAccept, isDragReject } = useDropzone({
    onDrop,
    noClick: true, // Prevent opening file dialog on click
    noKeyboard: true, // Prevent opening file dialog with keyboard
  });

  // Update drag active state
  useEffect(() => {
    setIsDragActive(isDragAccept || isDragReject);
  }, [isDragAccept, isDragReject]);

  // Cleanup effect to track component unmounting
  useEffect(() => {
    return () => {
      console.log('FileManager: Component unmounting');
    };
  }, []);

  // Handle folder creation
  const handleCreateFolder = useCallback(async (folderName: string, description: string) => {
    try {
      setLoading(true);

      await axios.post(
        `${API_URL}/api/documents/farm/${farmId || user?.farm_id}/folders`,
        {
          name: folderName,
          description,
          parent_folder_id: currentFolder
        },
        {
          headers: {
            Authorization: `Bearer ${localStorage.getItem('token')}`
          }
        }
      );

      // Invalidate cache before refreshing
      invalidateCache();

      // Refresh items
      await fetchItems();
      setCreateFolderModalOpen(false);
    } catch (err: any) {
      console.error('Error creating folder:', err);
      setError(err.response?.data?.error || 'Failed to create folder. Please try again later.');
    } finally {
      setLoading(false);
    }
  }, [farmId, user?.farm_id, currentFolder, fetchItems, invalidateCache]);

  // Handle document creation
  const handleCreateDocument = useCallback(async (documentName: string, content: string) => {
    try {
      setLoading(true);

      // Create a Blob from the content
      const blob = new Blob([content], { type: 'text/plain' });

      // Create a File object from the Blob
      const file = new File([blob], documentName, { type: 'text/plain' });

      // Create FormData to upload the file
      const formData = new FormData();
      formData.append('files', file);

      if (currentFolder) {
        formData.append('folderId', currentFolder);
      }

      await axios.post(
        `${API_URL}/api/documents/farm/${farmId || user?.farm_id}/upload`,
        formData,
        {
          headers: {
            'Content-Type': 'multipart/form-data',
            Authorization: `Bearer ${localStorage.getItem('token')}`
          }
        }
      );

      // Invalidate cache before refreshing
      invalidateCache();

      // Refresh items
      await fetchItems();
      setCreateDocumentModalOpen(false);
    } catch (err: any) {
      console.error('Error creating document:', err);
      setError(err.response?.data?.error || 'Failed to create document. Please try again later.');
    } finally {
      setLoading(false);
    }
  }, [farmId, user?.farm_id, currentFolder, fetchItems, invalidateCache]);

  // Handle item rename
  const handleRename = useCallback(async (item: FileItem, newName: string) => {
    try {
      setLoading(true);

      if (item.type === 'folder') {
        await axios.put(
          `${API_URL}/api/documents/folders/${item.id}`,
          {
            name: newName,
            description: item.description
          },
          {
            headers: {
              Authorization: `Bearer ${localStorage.getItem('token')}`
            }
          }
        );
      } else {
        await axios.put(
          `${API_URL}/api/documents/documents/${item.id}`,
          {
            name: newName,
            description: item.description
          },
          {
            headers: {
              Authorization: `Bearer ${localStorage.getItem('token')}`
            }
          }
        );
      }

      // Invalidate cache before refreshing
      invalidateCache();

      // Refresh items
      await fetchItems();
      setRenameModalOpen(false);
      setItemToRename(null);
    } catch (err: any) {
      console.error('Error renaming item:', err);
      setError(err.response?.data?.error || 'Failed to rename item. Please try again later.');
    } finally {
      setLoading(false);
    }
  }, [fetchItems, invalidateCache]);

  // Handle item delete
  const handleDelete = useCallback(async (items: FileItem[]) => {
    try {
      setLoading(true);

      for (const item of items) {
        if (item.type === 'folder') {
          await axios.delete(`${API_URL}/api/documents/folders/${item.id}`, {
            headers: {
              Authorization: `Bearer ${localStorage.getItem('token')}`
            }
          });
        } else {
          await axios.delete(`${API_URL}/api/documents/documents/${item.id}`, {
            headers: {
              Authorization: `Bearer ${localStorage.getItem('token')}`
            }
          });
        }
      }

      // Invalidate cache before refreshing
      invalidateCache();

      // Refresh items
      await fetchItems();

      // Update quota information after deletion
      fetchQuotaInfo();

      setDeleteModalOpen(false);
      setItemsToDelete([]);
      setSelectedItems([]);
    } catch (err: any) {
      console.error('Error deleting items:', err);
      setError(err.response?.data?.error || 'Failed to delete items. Please try again later.');
    } finally {
      setLoading(false);
    }
  }, [fetchItems, invalidateCache, fetchQuotaInfo]);

  // Handle item move (drag and drop)
  const handleMove = useCallback(async (itemIds: string[], targetFolderId: string | null) => {
    try {
      setLoading(true);

      for (const itemId of itemIds) {
        const item = items.find(i => i.id === itemId);
        if (!item) continue;

        if (item.type === 'folder') {
          await axios.put(
            `${API_URL}/api/documents/folders/${item.id}`,
            {
              name: item.name,
              description: item.description,
              parent_folder_id: targetFolderId
            },
            {
              headers: {
                Authorization: `Bearer ${localStorage.getItem('token')}`
              }
            }
          );
        } else {
          await axios.put(
            `${API_URL}/api/documents/documents/${item.id}`,
            {
              name: item.name,
              description: item.description,
              folder_id: targetFolderId
            },
            {
              headers: {
                Authorization: `Bearer ${localStorage.getItem('token')}`
              }
            }
          );
        }
      }

      // Invalidate cache before refreshing
      invalidateCache();

      // Refresh items
      await fetchItems();
      setSelectedItems([]);
    } catch (err: any) {
      console.error('Error moving items:', err);
      setError(err.response?.data?.error || 'Failed to move items. Please try again later.');
    } finally {
      setLoading(false);
    }
  }, [items, fetchItems, invalidateCache]);

  // Handle download
  const handleDownload = useCallback((item: FileItem) => {
    if (item.type === 'file') {
      window.open(`${API_URL}/api/documents/documents/${item.id}/download`, '_blank');
    }
  }, []);

  // Handle initiating move
  const handleInitiateMove = useCallback((items: FileItem[]) => {
    setItemsToMove(items);
    setMoveModalOpen(true);
  }, []);

  // Handle initiating permissions management
  const handleInitiatePermissions = useCallback((item: FileItem) => {
    setItemToManagePermissions(item);
    setPermissionModalOpen(true);
  }, []);

  // Handle initiating share
  const handleInitiateShare = useCallback((item: FileItem) => {
    // Don't allow sharing of secure folders
    if (item.type === 'folder' && item.is_secure) {
      setError('This folder is secure and cannot be shared with a public link.');
      return;
    }

    setItemToShare(item);
    setShareModalOpen(true);
  }, []);

  // Get selected items
  const getSelectedItems = useCallback(() => {
    return items.filter(item => selectedItems.includes(item.id));
  }, [items, selectedItems]);

  // Handle copy operation
  const handleCopy = useCallback((itemsToCopy: FileItem[]) => {
    setClipboardItems(itemsToCopy);
    setClipboardOperation('copy');
    setSelectedItems([]);

    // Show notification
    const itemCount = itemsToCopy.length;
    const itemType = itemCount === 1 ? (itemsToCopy[0].type === 'folder' ? 'folder' : 'file') : 'items';
    showNotification({
      type: 'success',
      message: `${itemCount} ${itemType} copied to clipboard`,
      duration: 2000
    });
  }, [showNotification]);

  // Handle cut operation
  const handleCut = useCallback((itemsToCut: FileItem[]) => {
    setClipboardItems(itemsToCut);
    setClipboardOperation('cut');
    setSelectedItems([]);

    // Show notification
    const itemCount = itemsToCut.length;
    const itemType = itemCount === 1 ? (itemsToCut[0].type === 'folder' ? 'folder' : 'file') : 'items';
    showNotification({
      type: 'success',
      message: `${itemCount} ${itemType} cut to clipboard`,
      duration: 2000
    });
  }, [showNotification]);

  // Handle paste operation
  const handlePaste = useCallback(async (targetFolderId: string | null) => {
    if (!clipboardItems.length || !clipboardOperation) return;

    try {
      setLoading(true);

      if (clipboardOperation === 'copy') {
        // For copy operation, we need to create new files/folders
        for (const item of clipboardItems) {
          if (item.type === 'folder') {
            // Create a new folder with the same name
            await axios.post(
              `${API_URL}/api/documents/farm/${farmId || user?.farm_id}/folders`,
              {
                name: item.name,
                description: item.description,
                parent_folder_id: targetFolderId
              },
              {
                headers: {
                  Authorization: `Bearer ${localStorage.getItem('token')}`
                }
              }
            );
          } else {
            // For files, we need to copy the file to the new location
            await axios.post(
              `${API_URL}/api/documents/documents/${item.id}/copy`,
              {
                folder_id: targetFolderId
              },
              {
                headers: {
                  Authorization: `Bearer ${localStorage.getItem('token')}`
                }
              }
            );
          }
        }
      } else if (clipboardOperation === 'cut') {
        // For cut operation, we move the items to the new location
        await handleMove(clipboardItems.map(item => item.id), targetFolderId);
      }

      // Show success notification
      const itemCount = clipboardItems.length;
      const itemType = itemCount === 1 ? (clipboardItems[0].type === 'folder' ? 'folder' : 'file') : 'items';
      const operation = clipboardOperation === 'copy' ? 'copied' : 'moved';
      showNotification({
        type: 'success',
        message: `${itemCount} ${itemType} ${operation} successfully`,
        duration: 3000
      });

      // Clear clipboard after paste
      setClipboardItems([]);
      setClipboardOperation(null);

      // Invalidate cache before refreshing
      invalidateCache();

      // Refresh items
      await fetchItems();
    } catch (err: any) {
      console.error('Error pasting items:', err);
      setError(err.response?.data?.error || 'Failed to paste items. Please try again later.');
    } finally {
      setLoading(false);
    }
  }, [clipboardItems, clipboardOperation, farmId, user?.farm_id, handleMove, invalidateCache, fetchItems, showNotification]);

  // Handle mouse down for drag selection
  const handleMouseDown = useCallback((e: React.MouseEvent) => {
    // Skip if right click or if clicking on an item directly (handled by the item's own click handler)
    if (e.button !== 0 || (e.target as HTMLElement).closest('.file-item')) {
      return;
    }

    // Start drag selection
    setIsDragSelecting(true);
    setDragStartPosition({ x: e.clientX, y: e.clientY });
    setDragCurrentPosition({ x: e.clientX, y: e.clientY });

    // Clear selection if not holding shift or ctrl/cmd
    if (!e.shiftKey && !e.ctrlKey && !e.metaKey) {
      setSelectedItems([]);
    }
  }, []);

  // Handle mouse move for drag selection
  const handleMouseMove = useCallback((e: React.MouseEvent) => {
    if (!isDragSelecting) return;

    setDragCurrentPosition({ x: e.clientX, y: e.clientY });

    // Update selected items based on the drag selection
    const itemsInSelection = items
      .filter(item => isItemInSelectionArea(item.id))
      .map(item => item.id);

    setSelectedItems(prev => {
      // If holding shift or ctrl/cmd, add to the existing selection
      if (e.shiftKey || e.ctrlKey || e.metaKey) {
        return [...new Set([...prev, ...itemsInSelection])];
      }
      // Otherwise, replace the selection
      return itemsInSelection;
    });
  }, [isDragSelecting, items, isItemInSelectionArea]);

  // Handle mouse up for drag selection
  const handleMouseUp = useCallback(() => {
    setIsDragSelecting(false);
    setDragStartPosition(null);
    setDragCurrentPosition(null);
  }, []);

  // Handle keyboard navigation
  const handleKeyDown = useCallback((e: React.KeyboardEvent) => {
    // Skip if modals are open or if inside an input field
    if (
      uploadModalOpen ||
      createFolderModalOpen ||
      renameModalOpen ||
      deleteModalOpen ||
      previewModalOpen ||
      moveModalOpen ||
      e.target instanceof HTMLInputElement ||
      e.target instanceof HTMLTextAreaElement
    ) {
      return;
    }

    const itemCount = items.length;
    if (itemCount === 0) return;

    // Calculate grid dimensions for arrow key navigation
    const calculateGridDimensions = () => {
      // This is an approximation - adjust based on your actual grid layout
      let columns = 1;
      const containerWidth = window.innerWidth;
      if (containerWidth >= 1280) columns = 5; // xl
      else if (containerWidth >= 1024) columns = 4; // lg
      else if (containerWidth >= 768) columns = 3; // md
      else if (containerWidth >= 640) columns = 2; // sm

      return { columns, rows: Math.ceil(itemCount / columns) };
    };

    switch (e.key) {
      case 'ArrowUp':
        e.preventDefault();
        if (viewMode === 'grid') {
          const { columns } = calculateGridDimensions();
          setFocusedItemIndex(prev => {
            const newIndex = prev - columns;
            return newIndex >= 0 ? newIndex : prev;
          });
        } else {
          setFocusedItemIndex(prev => (prev > 0 ? prev - 1 : prev));
        }
        break;

      case 'ArrowDown':
        e.preventDefault();
        if (viewMode === 'grid') {
          const { columns } = calculateGridDimensions();
          setFocusedItemIndex(prev => {
            const newIndex = prev + columns;
            return newIndex < itemCount ? newIndex : prev;
          });
        } else {
          setFocusedItemIndex(prev => (prev < itemCount - 1 ? prev + 1 : prev));
        }
        break;

      case 'ArrowLeft':
        e.preventDefault();
        if (viewMode === 'grid') {
          setFocusedItemIndex(prev => (prev > 0 ? prev - 1 : prev));
        }
        break;

      case 'ArrowRight':
        e.preventDefault();
        if (viewMode === 'grid') {
          setFocusedItemIndex(prev => (prev < itemCount - 1 ? prev + 1 : prev));
        }
        break;

      case 'Home':
        e.preventDefault();
        setFocusedItemIndex(0);
        break;

      case 'End':
        e.preventDefault();
        setFocusedItemIndex(itemCount - 1);
        break;

      case 'Enter':
      case ' ': // Space
        e.preventDefault();
        if (focusedItemIndex >= 0 && focusedItemIndex < itemCount) {
          const focusedItem = items[focusedItemIndex];
          if (focusedItem.type === 'folder') {
            navigateToFolder(focusedItem.id, focusedItem.name);
            if (onFolderOpen) onFolderOpen(focusedItem);
          } else {
            // Open preview for files
            setItemToPreview(focusedItem);
            setPreviewModalOpen(true);

            // Still call onFileOpen if provided (for compatibility)
            if (onFileOpen) onFileOpen(focusedItem);
          }
        }
        break;

      case 'Backspace':
        e.preventDefault();
        // Navigate to parent folder if not in root and no items selected
        if (selectedItems.length === 0 && breadcrumbs.length > 1) {
          const parentFolder = breadcrumbs[breadcrumbs.length - 2];
          navigateToFolder(parentFolder.id, parentFolder.name);
        }
        break;

      case 'a':
        // Select all items if Ctrl/Cmd+A
        if ((e.ctrlKey || e.metaKey) && !e.shiftKey && !e.altKey) {
          e.preventDefault();
          setSelectedItems(items.map(item => item.id));
        }
        break;

      case 'c':
        // Copy selected items with Ctrl/Cmd+C
        if ((e.ctrlKey || e.metaKey) && selectedItems.length > 0) {
          e.preventDefault();
          const itemsToCopy = items.filter(item => selectedItems.includes(item.id));
          handleCopy(itemsToCopy);
        }
        break;

      case 'x':
        // Cut selected items with Ctrl/Cmd+X
        if ((e.ctrlKey || e.metaKey) && selectedItems.length > 0) {
          e.preventDefault();
          const itemsToCut = items.filter(item => selectedItems.includes(item.id));
          handleCut(itemsToCut);
        }
        break;

      case 'v':
        // Paste items with Ctrl/Cmd+V
        if ((e.ctrlKey || e.metaKey) && clipboardItems.length > 0) {
          e.preventDefault();
          handlePaste(currentFolder);
        }
        break;

      case 'Delete':
        // Delete selected items with Delete key
        if (selectedItems.length > 0) {
          e.preventDefault();
          const itemsToDelete = items.filter(item => selectedItems.includes(item.id));
          setItemsToDelete(itemsToDelete);
          setDeleteModalOpen(true);
        }
        break;

      case 'Escape':
        // Clear selection
        e.preventDefault();
        setSelectedItems([]);
        break;

      default:
        break;
    }

    // If an item is focused but not selected, select it
    if (focusedItemIndex >= 0 && focusedItemIndex < itemCount) {
      const focusedItemId = items[focusedItemIndex].id;
      if (!selectedItems.includes(focusedItemId) &&
          !['ArrowUp', 'ArrowDown', 'ArrowLeft', 'ArrowRight', 'Home', 'End'].includes(e.key)) {
        setSelectedItems([focusedItemId]);
      }
    }
  }, [
    items,
    focusedItemIndex,
    viewMode,
    selectedItems,
    navigateToFolder,
    onFileOpen,
    onFolderOpen,
    breadcrumbs,
    uploadModalOpen,
    createFolderModalOpen,
    renameModalOpen,
    deleteModalOpen,
    previewModalOpen,
    moveModalOpen,
    setItemToPreview,
    setPreviewModalOpen,
    handleCopy,
    handleCut,
    handlePaste,
    clipboardItems,
    currentFolder,
    setItemsToDelete,
    setDeleteModalOpen
  ]);

  // Reset focused item index when items change
  useEffect(() => {
    setFocusedItemIndex(items.length > 0 ? 0 : -1);
  }, [items]);

  // Define context menu items for the entire file explorer area
  const getContextMenuItems = useCallback(() => {
    return [
      {
        label: 'Upload Files',
        icon: (
          <svg className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-8l-4-4m0 0l-4 4m4-4v12" />
          </svg>
        ),
        onClick: () => setUploadModalOpen(true)
      },
      {
        label: 'Create Folder',
        icon: (
          <svg className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 13h6m-3-3v6m-9-6h9m-9 6h9" />
          </svg>
        ),
        onClick: () => setCreateFolderModalOpen(true)
      },
      {
        label: 'Create Document',
        icon: (
          <svg className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
          </svg>
        ),
        onClick: () => setCreateDocumentModalOpen(true)
      },
      ...(clipboardItems.length > 0 && clipboardOperation !== null ? [
        {
          label: 'Paste',
          icon: (
            <svg className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
            </svg>
          ),
          onClick: () => handlePaste(currentFolder)
        }
      ] : []),
      {
        label: 'View Folder Permissions',
        icon: (
          <svg className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
          </svg>
        ),
        onClick: () => {
          // Create a dummy folder item for the current folder
          if (currentFolder) {
            const folderItem: FileItem = {
              id: currentFolder,
              name: breadcrumbs[breadcrumbs.length - 1].name,
              description: '',
              type: 'folder',
              farm_id: farmId || user?.farm_id || '',
              created_at: new Date().toISOString(),
              updated_at: new Date().toISOString()
            };
            setItemToManagePermissions(folderItem);
            setPermissionModalOpen(true);
          }
        },
        disabled: !currentFolder
      }
    ];
  }, [
    currentFolder,
    clipboardItems,
    clipboardOperation,
    handlePaste,
    setUploadModalOpen,
    setCreateFolderModalOpen,
    setCreateDocumentModalOpen,
    breadcrumbs,
    farmId,
    user?.farm_id,
    setItemToManagePermissions,
    setPermissionModalOpen
  ]);

  // Define context menu items for the file explorer area
  const fileExplorerContextMenuItems = [
    {
      label: 'Upload Files',
      icon: (
        <svg className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-8l-4-4m0 0l-4 4m4-4v12" />
        </svg>
      ),
      onClick: () => setUploadModalOpen(true)
    },
    {
      label: 'Create Folder',
      icon: (
        <svg className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 13h6m-3-3v6m-9-6h9m-9 6h9" />
        </svg>
      ),
      onClick: () => setCreateFolderModalOpen(true)
    },
    {
      label: 'Create Document',
      icon: (
        <svg className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
        </svg>
      ),
      onClick: () => setCreateDocumentModalOpen(true)
    }
  ];

  // Add paste option if there are items in the clipboard
  if (clipboardItems.length > 0 && clipboardOperation !== null) {
    fileExplorerContextMenuItems.push({
      label: 'Paste',
      icon: (
        <svg className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
        </svg>
      ),
      onClick: () => handlePaste(currentFolder)
    });
  }

  // Add view permissions option if we're in a folder
  if (currentFolder) {
    fileExplorerContextMenuItems.push({
      label: 'View Folder Permissions',
      icon: (
        <svg className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
        </svg>
      ),
      onClick: () => {
        // Create a dummy folder item for the current folder
        const folderItem: FileItem = {
          id: currentFolder,
          name: breadcrumbs[breadcrumbs.length - 1].name,
          description: '',
          type: 'folder',
          farm_id: farmId || user?.farm_id || '',
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        };
        setItemToManagePermissions(folderItem);
        setPermissionModalOpen(true);
      }
    });
  }

  // State for global context menu
  const [globalContextMenu, setGlobalContextMenu] = useState<{
    isVisible: boolean;
    x: number;
    y: number;
    items: any[];
  }>({
    isVisible: false,
    x: 0,
    y: 0,
    items: []
  });

  // Add event listener for the showContextMenu event
  useEffect(() => {
    const handleShowContextMenu = (e: CustomEvent) => {
      const { x, y, items } = e.detail;
      // Show the context menu at the specified position with the specified items
      setGlobalContextMenu({
        isVisible: true,
        x,
        y,
        items
      });
    };

    // Add event listener
    document.addEventListener('showContextMenu', handleShowContextMenu as EventListener);

    // Clean up
    return () => {
      document.removeEventListener('showContextMenu', handleShowContextMenu as EventListener);
    };
  }, []);

  // Ref for the global context menu
  const globalContextMenuRef = useRef<HTMLDivElement>(null);

  // Handle click outside to close the global context menu
  useEffect(() => {
    const handleClickOutside = (e: MouseEvent) => {
      if (
        globalContextMenu.isVisible &&
        globalContextMenuRef.current &&
        !globalContextMenuRef.current.contains(e.target as Node)
      ) {
        setGlobalContextMenu(prev => ({ ...prev, isVisible: false }));
      }
    };

    // Use mouseup instead of mousedown to avoid closing the menu when it's opened
    document.addEventListener('mouseup', handleClickOutside);

    // Also close on escape key
    const handleEscape = (e: KeyboardEvent) => {
      if (e.key === 'Escape' && globalContextMenu.isVisible) {
        setGlobalContextMenu(prev => ({ ...prev, isVisible: false }));
      }
    };

    document.addEventListener('keydown', handleEscape);

    return () => {
      document.removeEventListener('mouseup', handleClickOutside);
      document.removeEventListener('keydown', handleEscape);
    };
  }, [globalContextMenu.isVisible]);

  return (
    <DndProvider backend={HTML5Backend}>
      <ContextMenu items={fileExplorerContextMenuItems}>
        <div
          {...getRootProps()}
          className={`bg-white rounded-lg shadow relative ${className} ${isDragActive ? 'ring-2 ring-primary-500 ring-opacity-50' : ''}`}
          tabIndex={0} // Make the div focusable
          onKeyDown={handleKeyDown} // Attach keyboard event handler
        >
          <input {...getInputProps()} />

          {/* Drag overlay */}
          {isDragActive && (
            <div className="absolute inset-0 bg-primary-100 bg-opacity-70 flex items-center justify-center z-10 pointer-events-none rounded-lg">
              <div className="bg-white p-6 rounded-lg shadow-lg text-center">
                <svg
                  className="mx-auto h-12 w-12 text-primary-500 mb-4"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"
                  />
                </svg>
                <h3 className="text-lg font-medium text-gray-900 mb-1">Drop files here</h3>
                <p className="text-sm text-gray-500">Drop files to upload them to this folder</p>
              </div>
            </div>
          )}

          {/* Drag selection overlay */}
          {isDragSelecting && dragStartPosition && dragCurrentPosition && (
            <div
              className="absolute bg-primary-500 bg-opacity-20 border border-primary-500 border-opacity-50 pointer-events-none z-10"
              style={{
                left: Math.min(dragStartPosition.x, dragCurrentPosition.x) + 'px',
                top: Math.min(dragStartPosition.y, dragCurrentPosition.y) + 'px',
                width: Math.abs(dragCurrentPosition.x - dragStartPosition.x) + 'px',
                height: Math.abs(dragCurrentPosition.y - dragStartPosition.y) + 'px',
                position: 'fixed'
              }}
            />
          )}

          {/* Toolbar */}
          <FileManagerToolbar
            viewMode={viewMode}
            onViewModeChange={handleViewModeChange}
            onUpload={() => setUploadModalOpen(true)}
            onCreateFolder={() => setCreateFolderModalOpen(true)}
            onCreateDocument={() => setCreateDocumentModalOpen(true)}
            onSearch={handleSearch}
            searchQuery={searchQuery}
            selectedItems={getSelectedItems()}
            onDownload={handleDownload}
            onRename={(item) => {
              setItemToRename(item);
              setRenameModalOpen(true);
            }}
            onDelete={(items) => {
              setItemsToDelete(items);
              setDeleteModalOpen(true);
            }}
            onMove={handleInitiateMove}
            onManagePermissions={handleInitiatePermissions}
            onCopy={handleCopy}
            onCut={handleCut}
            onPaste={() => handlePaste(currentFolder)}
            canPaste={clipboardItems.length > 0 && clipboardOperation !== null}
            quotaInfo={quotaInfo}
            quotaLoading={quotaLoading}
          />

          {/* Breadcrumb */}
          <FileManagerBreadcrumb
            items={breadcrumbs}
            onNavigate={navigateToFolder}
          />

          {/* Error message */}
          {error && (
            <div className="m-4 p-4 text-sm text-red-700 bg-red-100 rounded-md flex items-center">
              <svg className="h-5 w-5 mr-2 text-red-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
              </svg>
              <span>{error}</span>
              <button
                onClick={() => setError(null)}
                className="ml-auto text-red-700 hover:text-red-900"
                aria-label="Dismiss error"
              >
                <svg className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>
          )}

          {/* Loading state */}
          {loading ? (
            <div className="flex flex-col justify-center items-center h-64">
              <svg
                className="animate-spin h-10 w-10 text-primary-500 mb-4"
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
              >
                <circle
                  className="opacity-25"
                  cx="12"
                  cy="12"
                  r="10"
                  stroke="currentColor"
                  strokeWidth="4"
                ></circle>
                <path
                  className="opacity-75"
                  fill="currentColor"
                  d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                ></path>
              </svg>
              <p className="text-gray-600">Loading files and folders...</p>
            </div>
          ) : (
            <div className="flex">
              {/* Folder Explorer Sidebar */}
              <div className="w-64 border-r border-gray-200 p-4 overflow-auto" style={{ maxHeight: 'calc(100vh - 200px)' }}>
                <FileManagerFolderExplorer
                  farmId={farmId || user?.farm_id || ''}
                  currentFolder={currentFolder}
                  onNavigate={navigateToFolder}
                  onMove={handleMove}
                />
              </div>

              {/* Main Content */}
              <div
                className="flex-1 p-4"
                onMouseDown={handleMouseDown}
                onMouseMove={handleMouseMove}
                onMouseUp={handleMouseUp}
                onMouseLeave={handleMouseUp}>
                {/* Empty state */}
                {items.length === 0 && (
                  <div className="flex flex-col items-center justify-center h-64 bg-gray-50 rounded-lg border-2 border-dashed border-gray-300">
                    <svg
                      className="h-12 w-12 text-gray-400 mb-2"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"
                      />
                    </svg>
                    <p className="text-gray-500 mb-1">No files or folders found</p>
                    <p className="text-gray-400 text-sm mb-3">Drag and drop files here or use the buttons below</p>
                    <div className="flex space-x-2">
                      <button
                        onClick={() => setUploadModalOpen(true)}
                        className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
                      >
                        Upload files
                      </button>
                      <button
                        onClick={() => setCreateFolderModalOpen(true)}
                        className="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md shadow-sm text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
                      >
                        Create folder
                      </button>
                    </div>
                  </div>
                )}

                {/* File/Folder Grid or List */}
                {viewMode === 'grid' ? (
                  <FileManagerGrid
                    items={items}
                    selectedItems={selectedItems}
                    focusedItemIndex={focusedItemIndex}
                    onSelect={handleItemSelect}
                    onDoubleClick={handleItemDoubleClick}
                    onMove={handleMove}
                    currentFolder={currentFolder}
                    onManagePermissions={handleInitiatePermissions}
                    onShare={handleInitiateShare}
                    onCopy={handleCopy}
                    onCut={handleCut}
                    onPaste={handlePaste}
                    canPaste={clipboardItems.length > 0 && clipboardOperation !== null}
                    clipboardItems={clipboardItems}
                    clipboardOperation={clipboardOperation}
                    registerItemRef={registerItemRef}
                  />
                ) : (
                  <FileManagerList
                    items={items}
                    selectedItems={selectedItems}
                    focusedItemIndex={focusedItemIndex}
                    onSelect={handleItemSelect}
                    onDoubleClick={handleItemDoubleClick}
                    onSort={handleSortChange}
                    sortBy={sortBy}
                    sortDirection={sortDirection}
                    onMove={handleMove}
                    currentFolder={currentFolder}
                    onManagePermissions={handleInitiatePermissions}
                    onCopy={handleCopy}
                    onCut={handleCut}
                    onPaste={handlePaste}
                    canPaste={clipboardItems.length > 0 && clipboardOperation !== null}
                    clipboardItems={clipboardItems}
                    clipboardOperation={clipboardOperation}
                    registerItemRef={registerItemRef}
                  />
                )}
              </div>
            </div>
          )}

          {/* Modals */}
          <FileManagerUploadModal
            isOpen={uploadModalOpen}
            onClose={() => setUploadModalOpen(false)}
            onUpload={(files) => handleUpload(files, currentFolder)}
          />

          <FileManagerCreateFolderModal
            isOpen={createFolderModalOpen}
            onClose={() => setCreateFolderModalOpen(false)}
            onCreateFolder={handleCreateFolder}
          />

          <FileManagerCreateDocumentModal
            isOpen={createDocumentModalOpen}
            onClose={() => setCreateDocumentModalOpen(false)}
            onCreateDocument={handleCreateDocument}
          />

          {itemToRename && (
            <FileManagerRenameModal
              isOpen={renameModalOpen}
              onClose={() => {
                setRenameModalOpen(false);
                setItemToRename(null);
              }}
              item={itemToRename}
              onRename={handleRename}
            />
          )}

          <FileManagerDeleteModal
            isOpen={deleteModalOpen}
            onClose={() => {
              setDeleteModalOpen(false);
              setItemsToDelete([]);
            }}
            items={itemsToDelete}
            onDelete={handleDelete}
          />

          {/* File Preview Modal */}
          <FileManagerPreview
            isOpen={previewModalOpen}
            onClose={() => {
              setPreviewModalOpen(false);
              setItemToPreview(null);
            }}
            file={itemToPreview}
          />

          {/* Move Modal */}
          <FileManagerMoveModal
            isOpen={moveModalOpen}
            onClose={() => {
              setMoveModalOpen(false);
              setItemsToMove([]);
            }}
            items={itemsToMove}
            onMove={handleMove}
            currentFolder={currentFolder}
            farmId={farmId || user?.farm_id || ''}
          />

          {/* Permission Modal */}
          <FileManagerPermissionModal
            isOpen={permissionModalOpen}
            onClose={() => {
              setPermissionModalOpen(false);
              setItemToManagePermissions(null);
            }}
            item={itemToManagePermissions}
          />

          {/* Share Modal */}
          <FileManagerShareModal
            isOpen={shareModalOpen}
            onClose={() => {
              setShareModalOpen(false);
              setItemToShare(null);
            }}
            item={itemToShare}
          />
        </div>
      </ContextMenu>
    </DndProvider>
  );
};

export default FileManager;
