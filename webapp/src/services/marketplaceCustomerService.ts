import axios from 'axios';
import { API_URL } from '../config';

// Types for marketplace customer data
export interface MarketplaceCustomer {
  id: string;
  name: string;
  email: string;
  phone?: string;
  farm_id: string;
  origin: string;
  global_customer_id?: string;
  addresses?: CustomerAddress[];
}

export interface CustomerAddress {
  id: string;
  farm_id: string;
  farm_alias?: string;
  address: string;
  city: string;
  state: string;
  zip_code: string;
  country: string;
  delivery_instructions?: string;
  access_code?: string;
  contact_name?: string;
  contact_phone?: string;
  is_default: boolean;
}

export interface CustomerOrder {
  id: string;
  customer_id: string;
  farm_id: string;
  status: string;
  notes?: string;
  fulfillment_method: string;
  pickup_date?: string;
  delivery_address_id?: string;
  created_at: string;
  updated_at: string;
  farm?: {
    id: string;
    name: string;
  };
}

export interface LoginResponse {
  token: string;
  customer: {
    id: string;
    name: string;
    email: string;
    farm: {
      id: string;
      name: string;
    };
  };
}

export interface RegisterRequest {
  name: string;
  email: string;
  password: string;
  phone?: string;
  farmId: string;
}

export interface LoginRequest {
  email: string;
  password: string;
  farmId: string;
}

export interface AddressRequest {
  farmId: string;
  farmAlias?: string;
  address: string;
  city: string;
  state: string;
  zipCode: string;
  country?: string;
  deliveryInstructions?: string;
  accessCode?: string;
  contactName?: string;
  contactPhone?: string;
  isDefault?: boolean;
}

export interface UpdateProfileRequest {
  name?: string;
  phone?: string;
}

/**
 * Register a new marketplace customer
 * @param customerData Customer registration data
 * @returns Promise with registration result
 */
export const registerCustomer = async (customerData: RegisterRequest): Promise<{ customerId: string; message: string }> => {
  try {
    const response = await axios.post(`${API_URL}/marketplace/customers/register`, customerData);
    return response.data;
  } catch (error) {
    console.error('Error registering marketplace customer:', error);
    throw error;
  }
};

/**
 * Login as a marketplace customer
 * @param credentials Login credentials
 * @returns Promise with login result including token and customer info
 */
export const loginCustomer = async (credentials: LoginRequest): Promise<LoginResponse> => {
  try {
    const response = await axios.post(`${API_URL}/marketplace/customers/login`, credentials);
    return response.data;
  } catch (error) {
    console.error('Error logging in marketplace customer:', error);
    throw error;
  }
};

/**
 * Get the current customer's profile
 * @returns Promise with customer profile data
 */
export const getCustomerProfile = async (): Promise<{ customer: MarketplaceCustomer }> => {
  try {
    const response = await axios.get(`${API_URL}/marketplace/customers/profile`, {
      headers: {
        Authorization: `Bearer ${localStorage.getItem('token')}`
      }
    });
    return response.data;
  } catch (error) {
    console.error('Error fetching customer profile:', error);
    throw error;
  }
};

/**
 * Update the current customer's profile
 * @param profileData Updated profile data
 * @returns Promise with update result
 */
export const updateCustomerProfile = async (profileData: UpdateProfileRequest): Promise<{ message: string; customer: Partial<MarketplaceCustomer> }> => {
  try {
    const response = await axios.put(`${API_URL}/marketplace/customers/profile`, profileData, {
      headers: {
        Authorization: `Bearer ${localStorage.getItem('token')}`
      }
    });
    return response.data;
  } catch (error) {
    console.error('Error updating customer profile:', error);
    throw error;
  }
};

/**
 * Add a new address for the current customer
 * @param addressData Address data
 * @returns Promise with the created address
 */
export const addCustomerAddress = async (addressData: AddressRequest): Promise<{ message: string; address: CustomerAddress }> => {
  try {
    const response = await axios.post(`${API_URL}/marketplace/customers/addresses`, addressData, {
      headers: {
        Authorization: `Bearer ${localStorage.getItem('token')}`
      }
    });
    return response.data;
  } catch (error) {
    console.error('Error adding customer address:', error);
    throw error;
  }
};

/**
 * Update an existing address
 * @param addressId ID of the address to update
 * @param addressData Updated address data
 * @returns Promise with the updated address
 */
export const updateCustomerAddress = async (addressId: string, addressData: Partial<AddressRequest>): Promise<{ message: string; address: CustomerAddress }> => {
  try {
    const response = await axios.put(`${API_URL}/marketplace/customers/addresses/${addressId}`, addressData, {
      headers: {
        Authorization: `Bearer ${localStorage.getItem('token')}`
      }
    });
    return response.data;
  } catch (error) {
    console.error('Error updating customer address:', error);
    throw error;
  }
};

/**
 * Delete an address
 * @param addressId ID of the address to delete
 * @returns Promise with deletion result
 */
export const deleteCustomerAddress = async (addressId: string): Promise<{ message: string }> => {
  try {
    const response = await axios.delete(`${API_URL}/marketplace/customers/addresses/${addressId}`, {
      headers: {
        Authorization: `Bearer ${localStorage.getItem('token')}`
      }
    });
    return response.data;
  } catch (error) {
    console.error('Error deleting customer address:', error);
    throw error;
  }
};

/**
 * Get order history for the current customer
 * @returns Promise with customer orders
 */
export const getCustomerOrders = async (): Promise<{ orders: CustomerOrder[] }> => {
  try {
    const response = await axios.get(`${API_URL}/marketplace/customers/orders`, {
      headers: {
        Authorization: `Bearer ${getAuthToken()}`
      }
    });
    return response.data;
  } catch (error) {
    console.error('Error fetching customer orders:', error);
    throw error;
  }
};

/**
 * Get details for a specific order
 * @param orderId ID of the order to retrieve
 * @returns Promise with order details
 */
export const getOrderDetails = async (orderId: string): Promise<any> => {
  try {
    const response = await axios.get(`${API_URL}/marketplace/customers/orders/${orderId}`, {
      headers: {
        Authorization: `Bearer ${getAuthToken()}`
      }
    });
    return response.data;
  } catch (error) {
    console.error('Error fetching order details:', error);
    throw error;
  }
};

/**
 * Get addresses for the current customer
 * @returns Promise with customer addresses
 */
export const getCustomerAddresses = async (): Promise<{ addresses: CustomerAddress[] }> => {
  try {
    const response = await axios.get(`${API_URL}/marketplace/customers/addresses`, {
      headers: {
        Authorization: `Bearer ${getAuthToken()}`
      }
    });
    return response.data;
  } catch (error) {
    console.error('Error fetching customer addresses:', error);
    throw error;
  }
};
