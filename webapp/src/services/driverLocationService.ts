import axios from 'axios';
import { API_URL } from '../config';
import { getAuthToken } from '../utils/storageUtils';
import { handleApiError } from '../utils/errorHandler';

// Define DriverLocation interface
export interface DriverLocation {
  id: string;
  driverId: string;
  latitude: number;
  longitude: number;
  accuracy: number;
  speed: number | null;
  heading: number | null;
  altitude: number | null;
  timestamp: string;
  farmId: string;
  tenantId: string;
  createdAt: string;
  updatedAt: string;
  driver?: {
    id: string;
    firstName: string;
    lastName: string;
  };
}

// Create a new driver location entry
export const createDriverLocation = async (
  data: {
    driverId: string;
    latitude: number;
    longitude: number;
    accuracy: number;
    speed?: number;
    heading?: number;
    altitude?: number;
    timestamp: string;
    farmId: string;
  }
) => {
  try {
    const token = localStorage.getItem('token');

    const response = await axios.post(`${API_URL}/driver-locations`, data, {
      headers: {
        Authorization: `Bearer ${token}`
      }
    });

    return response.data;
  } catch (error: unknown) {
    console.error('Error creating driver location:', error);

    // Use the error handler to get a structured error
    const structuredError = handleApiError(error, undefined, 'Failed to create driver location');

    // Create a custom error with the structured information
    const customError = new Error(structuredError.message);
    (customError as any).structuredError = structuredError;

    throw customError;
  }
};

// Get all driver locations
export const getDriverLocations = async (
  params: {
    farmId?: string;
    driverId?: string;
    startDate?: string;
    endDate?: string;
    page?: number;
    limit?: number;
    sortBy?: string;
    sortOrder?: 'asc' | 'desc';
  } = {}
) => {
  try {
    const token = localStorage.getItem('token');

    // Build query string
    const queryParams = new URLSearchParams();

    if (params.farmId) queryParams.append('farmId', params.farmId);
    if (params.driverId) queryParams.append('driverId', params.driverId);
    if (params.startDate) queryParams.append('startDate', params.startDate);
    if (params.endDate) queryParams.append('endDate', params.endDate);
    if (params.page) queryParams.append('page', params.page.toString());
    if (params.limit) queryParams.append('limit', params.limit.toString());
    if (params.sortBy) queryParams.append('sortBy', params.sortBy);
    if (params.sortOrder) queryParams.append('sortOrder', params.sortOrder);

    const queryString = queryParams.toString();
    const url = `${API_URL}/driver-locations${queryString ? `?${queryString}` : ''}`;

    const response = await axios.get(url, {
      headers: {
        Authorization: `Bearer ${token}`
      }
    });

    return Array.isArray(response.data) ? response.data : response.data.locations || [];
  } catch (error: unknown) {
    console.error('Error fetching driver locations:', error);

    // Use the error handler to get a structured error
    const structuredError = handleApiError(error, undefined, 'Failed to fetch driver locations');

    // Create a custom error with the structured information
    const customError = new Error(structuredError.message);
    (customError as any).structuredError = structuredError;

    throw customError;
  }
};

// Get the latest location for a specific driver
export const getLatestDriverLocation = async (driverId: string) => {
  try {
    const token = getAuthToken();

    const response = await axios.get(`${API_URL}/driver-locations/driver/${driverId}/latest`, {
      headers: {
        Authorization: `Bearer ${token}`
      }
    });

    return response.data;
  } catch (error: unknown) {
    console.error('Error fetching latest driver location:', error);

    // Use the error handler to get a structured error
    const structuredError = handleApiError(error, undefined, 'Failed to fetch latest driver location');

    // Create a custom error with the structured information
    const customError = new Error(structuredError.message);
    (customError as any).structuredError = structuredError;

    throw customError;
  }
};

// Get the location history for a specific driver
export const getDriverLocationHistory = async (
  driverId: string,
  params: {
    startDate?: string;
    endDate?: string;
    limit?: number;
  } = {}
) => {
  try {
    const token = localStorage.getItem('token');

    // Build query string
    const queryParams = new URLSearchParams();

    if (params.startDate) queryParams.append('startDate', params.startDate);
    if (params.endDate) queryParams.append('endDate', params.endDate);
    if (params.limit) queryParams.append('limit', params.limit.toString());

    const queryString = queryParams.toString();
    const url = `${API_URL}/driver-locations/driver/${driverId}/history${queryString ? `?${queryString}` : ''}`;

    const response = await axios.get(url, {
      headers: {
        Authorization: `Bearer ${token}`
      }
    });

    return Array.isArray(response.data) ? response.data : response.data.history || [];
  } catch (error: unknown) {
    console.error('Error fetching driver location history:', error);

    // Use the error handler to get a structured error
    const structuredError = handleApiError(error, undefined, 'Failed to fetch driver location history');

    // Create a custom error with the structured information
    const customError = new Error(structuredError.message);
    (customError as any).structuredError = structuredError;

    throw customError;
  }
};

// Delete a specific location entry
export const deleteDriverLocation = async (locationId: string) => {
  try {
    const token = localStorage.getItem('token');

    const response = await axios.delete(`${API_URL}/driver-locations/${locationId}`, {
      headers: {
        Authorization: `Bearer ${token}`
      }
    });

    return response.data;
  } catch (error: unknown) {
    console.error('Error deleting driver location:', error);

    // Use the error handler to get a structured error
    const structuredError = handleApiError(error, undefined, 'Failed to delete driver location');

    // Create a custom error with the structured information
    const customError = new Error(structuredError.message);
    (customError as any).structuredError = structuredError;

    throw customError;
  }
};

// Delete the entire location history for a specific driver
export const deleteDriverLocationHistory = async (driverId: string) => {
  try {
    const token = localStorage.getItem('token');

    const response = await axios.delete(`${API_URL}/driver-locations/driver/${driverId}/history`, {
      headers: {
        Authorization: `Bearer ${token}`
      }
    });

    return response.data;
  } catch (error: unknown) {
    console.error('Error deleting driver location history:', error);

    // Use the error handler to get a structured error
    const structuredError = handleApiError(error, undefined, 'Failed to delete driver location history');

    // Create a custom error with the structured information
    const customError = new Error(structuredError.message);
    (customError as any).structuredError = structuredError;

    throw customError;
  }
};

// Format date for display
export const formatDate = (dateString: string | null) => {
  if (!dateString) return 'N/A';
  const date = new Date(dateString);
  return date.toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  });
};