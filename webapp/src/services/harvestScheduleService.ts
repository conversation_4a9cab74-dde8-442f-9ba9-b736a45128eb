import axios from 'axios';
import { API_URL } from '../config';
import { getAuthToken } from '../utils/storageUtils';

// Define HarvestSchedule interface
export interface HarvestSchedule {
  id: string;
  farm_id: string;
  field_id: string;
  crop_id: string;
  scheduled_date: string;
  status: 'planned' | 'in_progress' | 'completed' | 'cancelled';
  weather_dependent: boolean;
  optimal_conditions?: {
    min_temperature?: number;
    max_temperature?: number;
    max_precipitation_chance?: number;
    max_wind_speed?: number;
    preferred_conditions?: string[];
  } | null;
  actual_start_date?: string | null;
  actual_end_date?: string | null;
  yield_amount?: number | null;
  yield_unit?: string | null;
  notes?: string | null;
  created_at: string;
  updated_at: string;
  farm?: {
    id: string;
    name: string;
  };
  field?: {
    id: string;
    name: string;
    size: number;
    size_unit: string;
  };
  crop?: {
    id: string;
    name: string;
    variety: string;
  };
}

// Define HarvestRecommendation interface
export interface HarvestRecommendation {
  scheduled_date: string;
  optimal_days: string[];
  warning_days: string[];
  reason: string;
  crop_name: string;
  crop_variety: string;
  field_name: string;
}

// Get all harvest schedules for a farm
export const getFarmHarvestSchedules = async (farmId: string) => {
  const token = localStorage.getItem('token');

  const response = await axios.get(`${API_URL}/harvest-schedules/farm/${farmId}`, {
    headers: {
      Authorization: `Bearer ${token}`
    }
  });

  return response.data.harvestSchedules;
};

// Get harvest schedules by crop
export const getHarvestSchedulesByCrop = async (cropId: string) => {
  const token = localStorage.getItem('token');

  const response = await axios.get(`${API_URL}/harvest-schedules/crop/${cropId}`, {
    headers: {
      Authorization: `Bearer ${token}`
    }
  });

  return response.data.harvestSchedules;
};

// Get harvest schedules by field
export const getHarvestSchedulesByField = async (fieldId: string) => {
  const token = localStorage.getItem('token');

  const response = await axios.get(`${API_URL}/harvest-schedules/field/${fieldId}`, {
    headers: {
      Authorization: `Bearer ${token}`
    }
  });

  return response.data.harvestSchedules;
};

// Get harvest schedules by status
export const getHarvestSchedulesByStatus = async (status: 'planned' | 'in_progress' | 'completed' | 'cancelled') => {
  const token = localStorage.getItem('token');

  const response = await axios.get(`${API_URL}/harvest-schedules/status/${status}`, {
    headers: {
      Authorization: `Bearer ${token}`
    }
  });

  return response.data.harvestSchedules;
};

// Get harvest schedules by date range
export const getHarvestSchedulesByDateRange = async (startDate: string, endDate: string) => {
  const token = getAuthToken();

  const response = await axios.get(`${API_URL}/harvest-schedules/date-range?startDate=${startDate}&endDate=${endDate}`, {
    headers: {
      Authorization: `Bearer ${token}`
    }
  });

  return response.data.harvestSchedules;
};

// Get a single harvest schedule by ID
export const getHarvestScheduleById = async (scheduleId: string) => {
  const token = getAuthToken();

  const response = await axios.get(`${API_URL}/harvest-schedules/${scheduleId}`, {
    headers: {
      Authorization: `Bearer ${token}`
    }
  });

  return response.data.harvestSchedule;
};

// Create a new harvest schedule
export const createHarvestSchedule = async (
  data: {
    farmId: string;
    fieldId: string;
    cropId: string;
    scheduledDate: string;
    status?: 'planned' | 'in_progress' | 'completed' | 'cancelled';
    weatherDependent?: boolean;
    optimalConditions?: {
      min_temperature?: number;
      max_temperature?: number;
      max_precipitation_chance?: number;
      max_wind_speed?: number;
      preferred_conditions?: string[];
    };
    notes?: string;
  }
) => {
  const token = getAuthToken();

  const response = await axios.post(`${API_URL}/harvest-schedules`, data, {
    headers: {
      Authorization: `Bearer ${token}`
    }
  });

  return response.data;
};

// Update a harvest schedule
export const updateHarvestSchedule = async (
  scheduleId: string,
  data: {
    scheduledDate?: string;
    status?: 'planned' | 'in_progress' | 'completed' | 'cancelled';
    weatherDependent?: boolean;
    optimalConditions?: {
      min_temperature?: number;
      max_temperature?: number;
      max_precipitation_chance?: number;
      max_wind_speed?: number;
      preferred_conditions?: string[];
    } | null;
    actualStartDate?: string | null;
    actualEndDate?: string | null;
    yieldAmount?: number | null;
    yieldUnit?: string | null;
    notes?: string | null;
  }
) => {
  const token = getAuthToken();

  const response = await axios.put(`${API_URL}/harvest-schedules/${scheduleId}`, data, {
    headers: {
      Authorization: `Bearer ${token}`
    }
  });

  return response.data;
};

// Delete a harvest schedule
export const deleteHarvestSchedule = async (scheduleId: string) => {
  const token = getAuthToken();

  const response = await axios.delete(`${API_URL}/harvest-schedules/${scheduleId}`, {
    headers: {
      Authorization: `Bearer ${token}`
    }
  });

  return response.data;
};

// Get weather-based recommendations for a harvest schedule
export const getHarvestRecommendations = async (scheduleId: string) => {
  const token = getAuthToken();

  const response = await axios.get(`${API_URL}/harvest-schedules/${scheduleId}/recommendations`, {
    headers: {
      Authorization: `Bearer ${token}`
    }
  });

  return response.data.recommendations as HarvestRecommendation;
};

// Format date for display
export const formatDate = (dateString: string | null) => {
  if (!dateString) return 'N/A';
  const date = new Date(dateString);
  return date.toLocaleDateString();
};

// Get status badge color
export const getStatusBadgeColor = (status: HarvestSchedule['status']) => {
  switch (status) {
    case 'planned':
      return 'bg-blue-100 text-blue-800';
    case 'in_progress':
      return 'bg-yellow-100 text-yellow-800';
    case 'completed':
      return 'bg-green-100 text-green-800';
    case 'cancelled':
      return 'bg-red-100 text-red-800';
    default:
      return 'bg-gray-100 text-gray-800';
  }
};