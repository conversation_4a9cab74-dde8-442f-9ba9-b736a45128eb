import React, { useState, useEffect } from 'react';
import { useAuth } from '../../context/AuthContext';
import { useFarm } from '../../context/FarmContext';
import Layout from '../../components/Layout';
import axios from 'axios';
import { API_URL } from '../../config';
import { Link } from 'react-router-dom';
import LoadingSpinner from '../../components/LoadingSpinner';
import { getAuthToken } from '../utils/storageUtils';

interface Field {
  id: string;
  name: string;
  area: number;
  area_unit: string;
  current_crop?: string;
  previous_crops?: string[];
}

interface RotationPlan {
  id: string;
  field_id: string;
  field_name: string;
  current_crop: string;
  recommended_sequence: string[];
  benefits: string[];
  rotation_years?: number;
  soil_health_impact?: string;
  confidence_score?: number;
  created_at: string;
}

const CropRotationOptimization = () => {
  const { user } = useAuth();
  const { currentFarm } = useFarm();
  const [fields, setFields] = useState<Field[]>([]);
  const [rotationPlans, setRotationPlans] = useState<RotationPlan[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedFieldId, setSelectedFieldId] = useState<string>('');

  useEffect(() => {
    if (user && currentFarm) {
      fetchFields();
      fetchRotationPlans();
    }
  }, [user, currentFarm]);

  const fetchFields = async () => {
    try {
      setLoading(true);
      const response = await axios.get(`${API_URL}/fields/farm/${currentFarm.id}`, {
        headers: {
          Authorization: `Bearer ${getAuthToken()}`
        }
      });
      setFields(Array.isArray(response.data) ? response.data : response.data.fields || []);
    } catch (err) {
      console.error('Error fetching fields:', err);
      setError('Failed to load fields. Please try again later.');
    } finally {
      setLoading(false);
    }
  };

  const fetchRotationPlans = async () => {
    try {
      setLoading(true);

      // Fetch real rotation plans from the backend API
      const response = await axios.get(`${API_URL}/crop-rotations`, {
        params: { farmId: currentFarm.id },
        headers: {
          Authorization: `Bearer ${getAuthToken()}`
        }
      });

      if (response.data.success) {
        setRotationPlans(response.data.rotationPlans || []);
      } else {
        throw new Error(response.data.message || 'Failed to fetch rotation plans');
      }
    } catch (err) {
      console.error('Error fetching rotation plans:', err);
      setError('Failed to load crop rotation plans. Please try again later.');

      // Fallback to mock data if API call fails
      const mockRotationPlans: RotationPlan[] = [
        {
          id: '1',
          field_id: '1',
          field_name: 'North Field',
          current_crop: 'Corn',
          recommended_sequence: ['Soybeans', 'Winter Wheat', 'Alfalfa', 'Corn'],
          benefits: [
            'Improved soil nitrogen levels',
            'Reduced pest pressure',
            'Enhanced soil structure',
            'Increased biodiversity'
          ],
          created_at: new Date().toISOString()
        },
        {
          id: '2',
          field_id: '2',
          field_name: 'East Field',
          current_crop: 'Soybeans',
          recommended_sequence: ['Winter Wheat', 'Cover Crop', 'Corn', 'Soybeans'],
          benefits: [
            'Balanced nutrient utilization',
            'Weed suppression',
            'Erosion control',
            'Improved water infiltration'
          ],
          created_at: new Date().toISOString()
        },
        {
          id: '3',
          field_id: '3',
          field_name: 'West Field',
          current_crop: 'Wheat',
          recommended_sequence: ['Cover Crop', 'Corn', 'Soybeans', 'Wheat'],
          benefits: [
            'Reduced disease pressure',
            'Improved soil organic matter',
            'Better nutrient cycling',
            'Increased yield potential'
          ],
          created_at: new Date().toISOString()
        }
      ];

      setRotationPlans(mockRotationPlans);
    } finally {
      setLoading(false);
    }
  };

  const generateRotationPlan = async () => {
    if (!selectedFieldId) {
      setError('Please select a field to generate a rotation plan');
      return;
    }

    try {
      setLoading(true);

      // Send request to the backend to generate a new rotation plan
      const response = await axios.post(`${API_URL}/crop-rotations/generate`, {
        farmId: currentFarm.id,
        fieldId: selectedFieldId
      }, {
        headers: {
          Authorization: `Bearer ${getAuthToken()}`
        }
      });

      if (response.data.success) {
        // Add the new rotation plan to the beginning of the list
        setRotationPlans([response.data.rotationPlan, ...rotationPlans]);
        setSelectedFieldId('');
        setError(null);
      } else {
        throw new Error(response.data.message || 'Failed to generate rotation plan');
      }
    } catch (err) {
      console.error('Error generating rotation plan:', err);
      setError('Failed to generate crop rotation plan. Please try again later.');

      // Fallback to generating a mock rotation plan if API call fails
      try {
        const selectedField = fields.find(field => field.id === selectedFieldId);

        const cropOptions = [
          'Corn', 'Soybeans', 'Wheat', 'Alfalfa', 'Barley', 'Oats', 
          'Rye', 'Sorghum', 'Cover Crop', 'Canola', 'Sunflower'
        ];

        // Get current crop or pick a random one if not available
        const currentCrop = selectedField?.current_crop || 
          cropOptions[Math.floor(Math.random() * cropOptions.length)];

        // Filter out the current crop for the rotation
        const availableCrops = cropOptions.filter(crop => crop !== currentCrop);

        // Generate a random sequence of 3-4 crops for rotation
        const sequenceLength = Math.floor(Math.random() * 2) + 3; // 3 or 4
        const recommendedSequence = [];

        for (let i = 0; i < sequenceLength; i++) {
          const randomIndex = Math.floor(Math.random() * availableCrops.length);
          recommendedSequence.push(availableCrops[randomIndex]);
          // Remove the selected crop to avoid duplicates
          availableCrops.splice(randomIndex, 1);

          // If we've used all available crops, reset the list
          if (availableCrops.length === 0) {
            availableCrops.push(...cropOptions.filter(crop => crop !== currentCrop));
          }
        }

        // Add the current crop at the end to complete the rotation cycle
        recommendedSequence.push(currentCrop);

        const benefitOptions = [
          'Improved soil nitrogen levels',
          'Reduced pest pressure',
          'Enhanced soil structure',
          'Increased biodiversity',
          'Balanced nutrient utilization',
          'Weed suppression',
          'Erosion control',
          'Improved water infiltration',
          'Reduced disease pressure',
          'Improved soil organic matter',
          'Better nutrient cycling',
          'Increased yield potential',
          'Reduced fertilizer requirements',
          'Improved soil microbial activity',
          'Enhanced drought resistance'
        ];

        // Randomly select 3-5 benefits
        const selectedBenefits = benefitOptions
          .sort(() => 0.5 - Math.random())
          .slice(0, Math.floor(Math.random() * 3) + 3);

        const newRotationPlan: RotationPlan = {
          id: Date.now().toString(),
          field_id: selectedFieldId,
          field_name: selectedField?.name || 'Unknown Field',
          current_crop: currentCrop,
          recommended_sequence: recommendedSequence,
          benefits: selectedBenefits,
          created_at: new Date().toISOString()
        };

        setRotationPlans([newRotationPlan, ...rotationPlans]);
        setSelectedFieldId('');
        setError(null);
      } catch (fallbackErr) {
        console.error('Error in fallback rotation plan generation:', fallbackErr);
      }
    } finally {
      setLoading(false);
    }
  };

  if (loading && fields.length === 0 && rotationPlans.length === 0) {
    return (
      <Layout>
        <div className="flex justify-center items-center h-full">
          <LoadingSpinner />
        </div>
      </Layout>
    );
  }

  return (
    <Layout>
      <div className="px-4 sm:px-6 lg:px-8 py-8">
        <div className="sm:flex sm:items-center">
          <div className="sm:flex-auto">
            <h1 className="text-2xl font-semibold text-gray-900">Crop Rotation Optimization</h1>
            <p className="mt-2 text-sm text-gray-700">
              AI-driven recommendations for optimal crop rotation schedules to improve soil health and crop yields.
            </p>
          </div>
          <div className="mt-4 sm:mt-0 sm:ml-16 sm:flex-none">
            <Link
              to="/fields"
              className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
            >
              View All Fields
            </Link>
          </div>
        </div>

        {error && (
          <div className="mt-4 bg-red-50 border-l-4 border-red-400 p-4">
            <div className="flex">
              <div className="flex-shrink-0">
                <svg className="h-5 w-5 text-red-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                </svg>
              </div>
              <div className="ml-3">
                <p className="text-sm text-red-700">{error}</p>
              </div>
            </div>
          </div>
        )}

        <div className="mt-8 bg-white shadow overflow-hidden sm:rounded-lg">
          <div className="px-4 py-5 sm:p-6">
            <h3 className="text-lg leading-6 font-medium text-gray-900">Generate New Rotation Plan</h3>
            <div className="mt-2 max-w-xl text-sm text-gray-500">
              <p>Select a field to generate an optimized crop rotation schedule based on soil conditions and crop history.</p>
            </div>
            <div className="mt-5 sm:flex sm:items-center">
              <div className="w-full sm:max-w-xs">
                <label htmlFor="field" className="sr-only">Field</label>
                <select
                  id="field"
                  name="field"
                  className="block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm rounded-md"
                  value={selectedFieldId}
                  onChange={(e) => setSelectedFieldId(e.target.value)}
                >
                  <option value="">Select a field</option>
                  {fields.map((field) => (
                    <option key={field.id} value={field.id}>
                      {field.name} ({field.area} {field.area_unit})
                    </option>
                  ))}
                </select>
              </div>
              <button
                type="button"
                onClick={generateRotationPlan}
                className="mt-3 w-full inline-flex items-center justify-center px-4 py-2 border border-transparent shadow-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm"
              >
                Generate Plan
              </button>
            </div>
          </div>
        </div>

        <div className="mt-8">
          <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4">Crop Rotation Plans</h3>
          {rotationPlans.length === 0 ? (
            <div className="bg-white shadow overflow-hidden sm:rounded-lg">
              <div className="px-4 py-5 sm:p-6 text-center text-gray-500">
                No rotation plans available. Generate a new plan to get started.
              </div>
            </div>
          ) : (
            <div className="space-y-6">
              {rotationPlans.map((plan) => (
                <div key={plan.id} className="bg-white shadow overflow-hidden sm:rounded-lg">
                  <div className="px-4 py-5 sm:px-6">
                    <h3 className="text-lg leading-6 font-medium text-gray-900">
                      {plan.field_name} - Rotation Plan
                    </h3>
                    <p className="mt-1 max-w-2xl text-sm text-gray-500">
                      Current crop: {plan.current_crop} | Created: {new Date(plan.created_at).toLocaleDateString()}
                      {plan.confidence_score && (
                        <span className="ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                          AI Confidence: {Math.round(plan.confidence_score)}%
                        </span>
                      )}
                    </p>
                  </div>
                  <div className="border-t border-gray-200 px-4 py-5 sm:p-0">
                    <dl className="sm:divide-y sm:divide-gray-200">
                      <div className="py-4 sm:py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                        <dt className="text-sm font-medium text-gray-500">Recommended Rotation Sequence</dt>
                        <dd className="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                          <div className="flex items-center space-x-2">
                            {plan.recommended_sequence.map((crop, index) => (
                              <React.Fragment key={index}>
                                <span className="px-2 py-1 bg-primary-100 text-primary-800 rounded">
                                  {crop}
                                </span>
                                {index < plan.recommended_sequence.length - 1 && (
                                  <svg className="h-4 w-4 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                                  </svg>
                                )}
                              </React.Fragment>
                            ))}
                          </div>
                        </dd>
                      </div>
                      <div className="py-4 sm:py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                        <dt className="text-sm font-medium text-gray-500">Benefits</dt>
                        <dd className="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                          <ul className="list-disc pl-5 space-y-1">
                            {plan.benefits.map((benefit, index) => (
                              <li key={index}>{benefit}</li>
                            ))}
                          </ul>
                        </dd>
                      </div>

                      {plan.soil_health_impact && (
                        <div className="py-4 sm:py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                          <dt className="text-sm font-medium text-gray-500">Soil Health Impact</dt>
                          <dd className="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                            <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                              plan.soil_health_impact === 'positive' 
                                ? 'bg-green-100 text-green-800' 
                                : plan.soil_health_impact === 'negative'
                                ? 'bg-red-100 text-red-800'
                                : 'bg-yellow-100 text-yellow-800'
                            }`}>
                              {plan.soil_health_impact.charAt(0).toUpperCase() + plan.soil_health_impact.slice(1)}
                            </span>
                          </dd>
                        </div>
                      )}

                      {plan.rotation_years && (
                        <div className="py-4 sm:py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                          <dt className="text-sm font-medium text-gray-500">Rotation Duration</dt>
                          <dd className="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                            {plan.rotation_years} {plan.rotation_years === 1 ? 'year' : 'years'}
                          </dd>
                        </div>
                      )}
                    </dl>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>
    </Layout>
  );
};

export default CropRotationOptimization;
