import { useState, useEffect, useContext } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import axios from 'axios';
import { AuthContext } from '../../context/AuthContext';
import { useFarm } from '../../context/FarmContext';
import Layout from '../../components/Layout';
import { API_URL } from '../../config';
import { getAuthToken } from '../utils/storageUtils';

interface Template {
  id: string;
  title: string;
  description: string;
  document_type: string;
  file_path: string;
  file_size: number;
  file_type: string;
  mime_type: string;
  version: number;
  created_by: string;
  created_at: string;
  updated_at: string;
  is_form_built: boolean;
  creator: {
    id: string;
    first_name: string;
    last_name: string;
    email: string;
  };
}

const TemplateList = () => {
  const [templates, setTemplates] = useState<Template[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [documentType, setDocumentType] = useState<string>('');

  const { user } = useContext(AuthContext);
  const { currentFarm } = useFarm();
  const navigate = useNavigate();

  // Fetch templates
  useEffect(() => {
    const fetchData = async () => {
      setLoading(true);
      setError(null);

      try {
        // Fetch templates
        let templatesUrl = `${API_URL}/document-signing/farm/${currentFarm?.id || user?.farm_id}/templates`;
        const params: Record<string, string> = {};

        if (searchQuery) {
          params.search = searchQuery;
        }

        if (documentType) {
          params.documentType = documentType;
        }

        // Add query parameters if any filters are applied
        if (Object.keys(params).length > 0) {
          const queryString = new URLSearchParams(params).toString();
          templatesUrl = `${templatesUrl}?${queryString}`;
        }

        const templatesResponse = await axios.get(templatesUrl, {
          headers: {
            Authorization: `Bearer ${getAuthToken()}`
          }
        });

        setTemplates(templatesResponse.data.templates || []);
      } catch (err: any) {
        console.error('Error fetching templates:', err);
        const errorMessage = err.response?.data?.error || 'Failed to load templates. Please try again later.';
        setError(errorMessage);
      } finally {
        setLoading(false);
      }
    };

    if (currentFarm?.id || user?.farm_id) {
      fetchData();
    } else {
      setError('No farm selected. Please select a farm to view templates.');
      setLoading(false);
    }
  }, [currentFarm?.id, user?.farm_id, searchQuery, documentType]);

  // Handle creating default templates
  const handleCreateDefaultTemplates = async () => {
    setLoading(true);
    setError(null);
    setSuccess(null);

    try {
      const response = await axios.post(
        `${API_URL}/document-signing/farm/${currentFarm?.id || user?.farm_id}/create-default-templates`,
        {},
        {
          headers: {
            Authorization: `Bearer ${getAuthToken()}`
          }
        }
      );

      setSuccess(response.data.message);

      // Refresh templates list
      const templatesResponse = await axios.get(
        `${API_URL}/document-signing/farm/${currentFarm?.id || user?.farm_id}/templates`,
        {
          headers: {
            Authorization: `Bearer ${getAuthToken()}`
          }
        }
      );

      setTemplates(templatesResponse.data.templates || []);
    } catch (err: any) {
      console.error('Error creating default templates:', err);
      const errorMessage = err.response?.data?.error || 'Failed to create default templates. Please try again later.';
      setError(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  // Handle creating a document from a template
  const handleCreateFromTemplate = (templateId: string, isFormBuilt: boolean = false) => {
    if (isFormBuilt) {
      navigate(`/documents/signing/from-form-template/${templateId}`);
    } else {
      navigate(`/documents/signing/from-template/${templateId}`);
    }
  };

  return (
    <Layout>
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold text-gray-900">Document Templates</h1>
        <div className="flex space-x-2">
          <button
            onClick={handleCreateDefaultTemplates}
            className="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md shadow-sm text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
          >
            Create Default Templates
          </button>
          <div className="relative group">
            <button
              className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
            >
              Create Template
              <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 ml-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
              </svg>
            </button>
            <div 
              className="absolute right-0 mt-0 pt-2 w-48 z-10 hidden group-hover:block"
              style={{ paddingTop: '8px' }}
            >
              <div className="bg-white rounded-md shadow-lg py-1">
                <Link
                  to="/documents/signing/new-template"
                  className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                >
                  Upload File Template
                </Link>
                <Link
                  to="/documents/signing/form-builder"
                  className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                >
                  Create Form Template
                </Link>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Search and filter */}
      <div className="mb-6 bg-white p-4 shadow rounded-md">
        <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
          <div>
            <label htmlFor="search" className="block text-sm font-medium text-gray-700">
              Search
            </label>
            <input
              type="text"
              id="search"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              placeholder="Search by title or description"
              className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
            />
          </div>
          <div>
            <label htmlFor="documentType" className="block text-sm font-medium text-gray-700">
              Document Type
            </label>
            <select
              id="documentType"
              value={documentType}
              onChange={(e) => setDocumentType(e.target.value)}
              className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
            >
              <option value="">All Types</option>
              <option value="agreement">Agreement</option>
              <option value="contract">Contract</option>
              <option value="lease">Lease</option>
              <option value="invoice">Invoice</option>
              <option value="receipt">Receipt</option>
              <option value="other">Other</option>
            </select>
          </div>
        </div>
      </div>

      {/* Success message */}
      {success && (
        <div className="mb-4 p-4 text-sm text-green-700 bg-green-100 rounded-md">
          <span>{success}</span>
        </div>
      )}

      {/* Error message */}
      {error && (
        <div className="mb-4 p-4 text-sm text-red-700 bg-red-100 rounded-md">
          <span>{error}</span>
        </div>
      )}

      {/* Loading state */}
      {loading ? (
        <div className="flex flex-col justify-center items-center h-64 bg-gray-50 rounded-lg border border-gray-200">
          <p className="text-gray-600">Loading templates...</p>
        </div>
      ) : (
        <>
          {/* Empty state */}
          {templates.length === 0 && !loading && (
            <div className="flex flex-col items-center justify-center h-64 bg-gray-50 rounded-lg border-2 border-dashed border-gray-300">
              <p className="text-gray-500 mb-2">No templates found</p>
              <div className="flex space-x-2">
                <button
                  onClick={handleCreateDefaultTemplates}
                  className="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md shadow-sm text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
                >
                  Create Default Templates
                </button>
                <div className="relative group">
                  <button
                    className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
                  >
                    Create Template
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 ml-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                    </svg>
                  </button>
                  <div 
                    className="absolute right-0 mt-0 pt-2 w-48 z-10 hidden group-hover:block"
                    style={{ paddingTop: '8px' }}
                  >
                    <div className="bg-white rounded-md shadow-lg py-1">
                      <Link
                        to="/documents/signing/new-template"
                        className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                      >
                        Upload File Template
                      </Link>
                      <Link
                        to="/documents/signing/form-builder"
                        className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                      >
                        Create Form Template
                      </Link>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Templates grid */}
          {templates.length > 0 && (
            <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-3">
              {templates.map((template) => (
                <div
                  key={template.id}
                  className="bg-white overflow-hidden shadow rounded-lg border border-gray-200"
                >
                  <div className="px-4 py-5 sm:p-6">
                    <h3 className="text-lg font-medium text-gray-900 truncate">{template.title}</h3>
                    <p className="mt-1 text-sm text-gray-500 h-12 overflow-hidden">
                      {template.description || 'No description'}
                    </p>
                    <div className="mt-4 flex items-center text-sm text-gray-500">
                      <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                        {template.document_type}
                      </span>
                      {template.is_form_built && (
                        <span className="ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                          Form Builder
                        </span>
                      )}
                      <span className="ml-2">
                        Created {new Date(template.created_at).toLocaleDateString()}
                      </span>
                    </div>
                  </div>
                  <div className="bg-gray-50 px-4 py-4 sm:px-6 flex justify-end space-x-2">
                    <button
                      onClick={() => handleCreateFromTemplate(template.id, template.is_form_built)}
                      className="inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded shadow-sm text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
                    >
                      Use Template
                    </button>
                    <Link
                      to={`/documents/signing/template/${template.id}`}
                      className="inline-flex items-center px-3 py-1.5 border border-gray-300 text-xs font-medium rounded shadow-sm text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
                    >
                      View Details
                    </Link>
                  </div>
                </div>
              ))}
            </div>
          )}
        </>
      )}
    </Layout>
  );
};

export default TemplateList;
