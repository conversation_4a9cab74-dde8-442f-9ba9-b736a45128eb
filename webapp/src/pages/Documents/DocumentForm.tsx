import { useState, useEffect, useContext, useCallback } from 'react';
import { Link, useParams, useNavigate } from 'react-router-dom';
import axios from 'axios';
import { AuthContext } from '../../context/AuthContext';
import Layout from '../../components/Layout';
import { API_URL } from '../../config';
import { getAuthToken } from '../utils/storageUtils';

interface Folder {
  id: string;
  name: string;
  parent_folder_id: string | null;
}

interface Document {
  id: string;
  name: string;
  description: string;
  file_path: string;
  file_size: number;
  file_type: string;
  mime_type: string;
  folder_id: string | null;
  farm_id: string;
  uploaded_by: string;
}

const DocumentForm = () => {
  const { id } = useParams<{ id: string }>();
  const isEditMode = !!id;

  const [name, setName] = useState('');
  const [description, setDescription] = useState('');
  const [file, setFile] = useState<File | null>(null);
  const [folderId, setFolderId] = useState<string | null>(null);
  const [folders, setFolders] = useState<Folder[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [document, setDocument] = useState<Document | null>(null);
  const [dragActive, setDragActive] = useState(false);

  const { user } = useContext(AuthContext);
  const navigate = useNavigate();

  // Fetch folders for dropdown
  useEffect(() => {
    const fetchFolders = async () => {
      try {
        const response = await axios.get(
          `${API_URL}/documents/farm/${user?.farm_id}/folders`,
          {
            headers: {
              Authorization: `Bearer ${getAuthToken()}`
            }
          }
        );

        setFolders(response.data || []);
      } catch (err: any) {
        console.error('Error fetching folders:', err);
        setError('Failed to load folders. Please try again later.');
      }
    };

    if (user?.farm_id) {
      fetchFolders();
    }
  }, [user?.farm_id]);

  // Fetch document details if in edit mode
  useEffect(() => {
    const fetchDocument = async () => {
      setLoading(true);
      setError(null);

      try {
        // Validate that id is a valid UUID before making the API call
        const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
        if (!id || !uuidRegex.test(id)) {
          throw new Error('Invalid document ID');
        }

        const response = await axios.get(`${API_URL}/documents/${id}`, {
          headers: {
            Authorization: `Bearer ${getAuthToken()}`
          }
        });

        const doc = response.data;
        setDocument(doc);
        setName(doc.name);
        setDescription(doc.description || '');
        setFolderId(doc.folder_id);
      } catch (err: any) {
        console.error('Error fetching document:', err);
        if (err.message === 'Invalid document ID') {
          setError('Invalid document ID. Please check the URL and try again.');
          // Redirect to documents list if ID is invalid
          navigate('/documents');
        } else {
          setError('Failed to load document. Please try again later.');
        }
      } finally {
        setLoading(false);
      }
    };

    if (isEditMode && id) {
      fetchDocument();
    }
  }, [isEditMode, id, navigate]);

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError(null);

    try {
      if (isEditMode) {
        // Validate that id is a valid UUID before making the API call
        const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
        if (!id || !uuidRegex.test(id)) {
          throw new Error('Invalid document ID');
        }

        // Update existing document
        await axios.put(
          `${API_URL}/documents/${id}`,
          {
            name,
            description,
            folderId: folderId || null
          },
          {
            headers: {
              Authorization: `Bearer ${getAuthToken()}`
            }
          }
        );

        navigate(`/documents/view/${id}`);
      } else {
        // Create new document
        if (!file) {
          setError('Please select a file to upload');
          setLoading(false);
          return;
        }

        const formData = new FormData();
        formData.append('file', file);
        formData.append('description', description);

        // Always include folderId in the request, even if it's null
        formData.append('folderId', folderId || 'null');

        const response = await axios.post(
          `${API_URL}/documents/farm/${user?.farm_id}/documents`,
          formData,
          {
            headers: {
              'Content-Type': 'multipart/form-data',
              Authorization: `Bearer ${getAuthToken()}`
            }
          }
        );

        navigate(`/documents/view/${response.data.document.id}`);
      }
    } catch (err: any) {
      console.error('Error saving document:', err);
      setError(err.response?.data?.error || 'Failed to save document. Please try again later.');
    } finally {
      setLoading(false);
    }
  };

  // Handle file selection
  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      const selectedFile = e.target.files[0];
      setFile(selectedFile);

      // If no name is set, use the file name
      if (!name) {
        setName(selectedFile.name);
      }
    }
  };

  // Handle drag events
  const handleDrag = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();

    if (e.type === 'dragenter' || e.type === 'dragover') {
      setDragActive(true);
    } else if (e.type === 'dragleave') {
      setDragActive(false);
    }
  }, []);

  // Handle drop event
  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);

    if (e.dataTransfer.files && e.dataTransfer.files[0]) {
      const droppedFile = e.dataTransfer.files[0];
      setFile(droppedFile);

      // If no name is set, use the file name
      if (!name) {
        setName(droppedFile.name);
      }
    }
  }, [name]);

  return (
    <Layout>
      <div className="flex justify-between items-center mb-6">
        <div className="flex items-center">
          <Link
            to="/documents"
            className="mr-4 text-gray-500 hover:text-gray-700"
          >
            <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M10 19l-7-7m0 0l7-7m-7 7h18"
              />
            </svg>
          </Link>
          <h1 className="text-2xl font-bold text-gray-900">
            {isEditMode ? 'Edit Document' : 'Upload Document'}
          </h1>
        </div>
      </div>

      {/* Error message */}
      {error && (
        <div className="mb-4 p-4 text-sm text-red-700 bg-red-100 rounded-md">
          {error}
        </div>
      )}

      {/* Loading state */}
      {loading && !isEditMode ? (
        <div className="flex justify-center items-center h-64">
          <svg
            className="animate-spin h-8 w-8 text-primary-500"
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            viewBox="0 0 24 24"
          >
            <circle
              className="opacity-25"
              cx="12"
              cy="12"
              r="10"
              stroke="currentColor"
              strokeWidth="4"
            ></circle>
            <path
              className="opacity-75"
              fill="currentColor"
              d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
            ></path>
          </svg>
        </div>
      ) : (
        <form onSubmit={handleSubmit} className="bg-white shadow overflow-hidden sm:rounded-lg p-6">
          <div className="grid grid-cols-1 gap-6">
            {/* Document name */}
            <div>
              <label htmlFor="name" className="block text-sm font-medium text-gray-700">
                Document Name
              </label>
              <input
                type="text"
                name="name"
                id="name"
                value={name}
                onChange={(e) => setName(e.target.value)}
                className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
                required
              />
            </div>

            {/* Document description */}
            <div>
              <label htmlFor="description" className="block text-sm font-medium text-gray-700">
                Description
              </label>
              <textarea
                name="description"
                id="description"
                value={description}
                onChange={(e) => setDescription(e.target.value)}
                rows={3}
                className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
              />
            </div>

            {/* Folder selection */}
            <div>
              <label htmlFor="folder" className="block text-sm font-medium text-gray-700">
                Folder
              </label>
              <select
                id="folder"
                name="folder"
                value={folderId || ''}
                onChange={(e) => setFolderId(e.target.value || null)}
                className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
              >
                <option value="">Root (No Folder)</option>
                {folders.map((folder) => (
                  <option key={folder.id} value={folder.id}>
                    {folder.name}
                  </option>
                ))}
              </select>
            </div>

            {/* File upload (only in create mode) */}
            {!isEditMode && (
              <div>
                <label className="block text-sm font-medium text-gray-700">
                  Document File
                </label>
                <div
                  className={`mt-1 flex justify-center px-6 pt-5 pb-6 border-2 border-gray-300 border-dashed rounded-md ${
                    dragActive ? 'bg-gray-50 border-primary-500' : ''
                  }`}
                  onDragEnter={handleDrag}
                  onDragOver={handleDrag}
                  onDragLeave={handleDrag}
                  onDrop={handleDrop}
                >
                  <div className="space-y-1 text-center">
                    <svg
                      className="mx-auto h-12 w-12 text-gray-400"
                      stroke="currentColor"
                      fill="none"
                      viewBox="0 0 48 48"
                      aria-hidden="true"
                    >
                      <path
                        d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02"
                        strokeWidth={2}
                        strokeLinecap="round"
                        strokeLinejoin="round"
                      />
                    </svg>
                    <div className="flex text-sm text-gray-600">
                      <label
                        htmlFor="file-upload"
                        className="relative cursor-pointer bg-white rounded-md font-medium text-primary-600 hover:text-primary-500 focus-within:outline-none focus-within:ring-2 focus-within:ring-offset-2 focus-within:ring-primary-500"
                      >
                        <span>Upload a file</span>
                        <input
                          id="file-upload"
                          name="file-upload"
                          type="file"
                          className="sr-only"
                          onChange={handleFileChange}
                          required
                        />
                      </label>
                      <p className="pl-1">or drag and drop</p>
                    </div>
                    <p className="text-xs text-gray-500">
                      PDF, Word, Excel, images, and other document formats
                    </p>
                    {file && (
                      <p className="text-sm text-primary-600">
                        Selected: {file.name} ({(file.size / 1024 / 1024).toFixed(2)} MB)
                      </p>
                    )}
                  </div>
                </div>
              </div>
            )}

            {/* Submit button */}
            <div className="flex justify-end">
              <Link
                to="/documents"
                className="mr-3 inline-flex justify-center py-2 px-4 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
              >
                Cancel
              </Link>
              <button
                type="submit"
                disabled={loading}
                className="inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50"
              >
                {loading ? (
                  <svg
                    className="animate-spin -ml-1 mr-2 h-4 w-4 text-white"
                    xmlns="http://www.w3.org/2000/svg"
                    fill="none"
                    viewBox="0 0 24 24"
                  >
                    <circle
                      className="opacity-25"
                      cx="12"
                      cy="12"
                      r="10"
                      stroke="currentColor"
                      strokeWidth="4"
                    ></circle>
                    <path
                      className="opacity-75"
                      fill="currentColor"
                      d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                    ></path>
                  </svg>
                ) : null}
                {isEditMode ? 'Save Changes' : 'Upload Document'}
              </button>
            </div>
          </div>
        </form>
      )}
    </Layout>
  );
};

export default DocumentForm;
