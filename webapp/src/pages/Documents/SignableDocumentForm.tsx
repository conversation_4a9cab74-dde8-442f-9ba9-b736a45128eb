import { useState, useEffect, useContext } from 'react';
import { useParams, useNavigate, Link } from 'react-router-dom';
import axios from 'axios';
import { AuthContext } from '../../context/AuthContext';
import { useFarm } from '../../context/FarmContext';
import Layout from '../../components/Layout';
import { API_URL } from '../../config';
import { getAuthToken } from '../utils/storageUtils';

interface Signer {
  id?: string;
  email: string;
  name: string;
  role?: string;
  order?: number;
}

const SignableDocumentForm = () => {
  const { id } = useParams<{ id: string }>();
  const isEditMode = !!id;
  
  const [title, setTitle] = useState('');
  const [description, setDescription] = useState('');
  const [documentType, setDocumentType] = useState('agreement');
  const [file, setFile] = useState<File | null>(null);
  const [signers, setSigners] = useState<Signer[]>([]);
  const [newSigner, setNewSigner] = useState<Signer>({ email: '', name: '', role: '' });
  
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);

  const { user } = useContext(AuthContext);
  const { currentFarm } = useFarm();
  const navigate = useNavigate();

  // Fetch document details if in edit mode
  useEffect(() => {
    if (isEditMode) {
      const fetchDocument = async () => {
        setLoading(true);
        try {
          const response = await axios.get(`${API_URL}/document-signing/signable-documents/${id}`, {
            headers: {
              Authorization: `Bearer ${getAuthToken()}`
            }
          });
          
          const document = response.data;
          setTitle(document.title);
          setDescription(document.description || '');
          setDocumentType(document.document_type);
          
          if (document.signers) {
            setSigners(document.signers.map((signer: any) => ({
              id: signer.id,
              email: signer.signer_email,
              name: signer.signer_name,
              role: signer.signer_role || '',
              order: signer.signer_order
            })));
          }
        } catch (err: any) {
          console.error('Error fetching document:', err);
          setError(err.response?.data?.error || 'Failed to load document. Please try again later.');
        } finally {
          setLoading(false);
        }
      };
      
      fetchDocument();
    }
  }, [id, isEditMode]);

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!currentFarm?.id && !user?.farm_id) {
      setError('No farm selected. Please select a farm to create a document.');
      return;
    }
    
    if (isEditMode && !file) {
      // In edit mode, file is optional
    } else if (!file) {
      setError('Please select a file to upload.');
      return;
    }
    
    setLoading(true);
    setError(null);
    
    try {
      const formData = new FormData();
      formData.append('title', title);
      formData.append('description', description);
      formData.append('documentType', documentType);
      
      if (file) {
        formData.append('file', file);
      }
      
      // Add signers to form data
      if (signers.length > 0) {
        formData.append('signers', JSON.stringify(signers));
      }
      
      let response;
      
      if (isEditMode) {
        // Update existing document
        response = await axios.put(
          `${API_URL}/document-signing/signable-documents/${id}`,
          formData,
          {
            headers: {
              'Content-Type': 'multipart/form-data',
              Authorization: `Bearer ${getAuthToken()}`
            }
          }
        );
        
        setSuccess('Document updated successfully');
      } else {
        // Create new document
        response = await axios.post(
          `${API_URL}/document-signing/farm/${currentFarm?.id || user?.farm_id}/signable-documents`,
          formData,
          {
            headers: {
              'Content-Type': 'multipart/form-data',
              Authorization: `Bearer ${getAuthToken()}`
            }
          }
        );
        
        setSuccess('Document created successfully');
      }
      
      // Navigate to document detail page after short delay
      setTimeout(() => {
        navigate(`/documents/signing/view/${response.data.document.id}`);
      }, 1500);
    } catch (err: any) {
      console.error('Error saving document:', err);
      setError(err.response?.data?.error || 'Failed to save document. Please try again later.');
    } finally {
      setLoading(false);
    }
  };

  // Handle file selection
  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files.length > 0) {
      setFile(e.target.files[0]);
    }
  };

  // Handle adding a new signer
  const handleAddSigner = () => {
    if (!newSigner.email || !newSigner.name) {
      setError('Signer email and name are required.');
      return;
    }
    
    setSigners([...signers, { ...newSigner, order: signers.length + 1 }]);
    setNewSigner({ email: '', name: '', role: '' });
  };

  // Handle removing a signer
  const handleRemoveSigner = (index: number) => {
    const updatedSigners = [...signers];
    updatedSigners.splice(index, 1);
    
    // Update order for remaining signers
    const reorderedSigners = updatedSigners.map((signer, idx) => ({
      ...signer,
      order: idx + 1
    }));
    
    setSigners(reorderedSigners);
  };

  return (
    <Layout>
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold text-gray-900">
          {isEditMode ? 'Edit Signable Document' : 'Create Signable Document'}
        </h1>
        <Link
          to="/documents/signing"
          className="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md shadow-sm text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
        >
          Back to Documents
        </Link>
      </div>

      {/* Error message */}
      {error && (
        <div className="mb-4 p-4 text-sm text-red-700 bg-red-100 rounded-md">
          <span>{error}</span>
        </div>
      )}

      {/* Success message */}
      {success && (
        <div className="mb-4 p-4 text-sm text-green-700 bg-green-100 rounded-md">
          <span>{success}</span>
        </div>
      )}

      <form onSubmit={handleSubmit} className="space-y-6 bg-white shadow sm:rounded-md sm:overflow-hidden">
        <div className="px-4 py-5 sm:p-6">
          <div className="grid grid-cols-1 gap-6">
            {/* Document Title */}
            <div>
              <label htmlFor="title" className="block text-sm font-medium text-gray-700">
                Document Title <span className="text-red-500">*</span>
              </label>
              <input
                type="text"
                id="title"
                value={title}
                onChange={(e) => setTitle(e.target.value)}
                required
                className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
              />
            </div>

            {/* Document Description */}
            <div>
              <label htmlFor="description" className="block text-sm font-medium text-gray-700">
                Description
              </label>
              <textarea
                id="description"
                value={description}
                onChange={(e) => setDescription(e.target.value)}
                rows={3}
                className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
              />
            </div>

            {/* Document Type */}
            <div>
              <label htmlFor="documentType" className="block text-sm font-medium text-gray-700">
                Document Type <span className="text-red-500">*</span>
              </label>
              <select
                id="documentType"
                value={documentType}
                onChange={(e) => setDocumentType(e.target.value)}
                required
                className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
              >
                <option value="agreement">Agreement</option>
                <option value="contract">Contract</option>
                <option value="lease">Lease</option>
                <option value="invoice">Invoice</option>
                <option value="receipt">Receipt</option>
                <option value="other">Other</option>
              </select>
            </div>

            {/* File Upload */}
            <div>
              <label htmlFor="file" className="block text-sm font-medium text-gray-700">
                Document File {!isEditMode && <span className="text-red-500">*</span>}
              </label>
              <div className="mt-1 flex justify-center px-6 pt-5 pb-6 border-2 border-gray-300 border-dashed rounded-md">
                <div className="space-y-1 text-center">
                  <svg
                    className="mx-auto h-12 w-12 text-gray-400"
                    stroke="currentColor"
                    fill="none"
                    viewBox="0 0 48 48"
                    aria-hidden="true"
                  >
                    <path
                      d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02"
                      strokeWidth={2}
                      strokeLinecap="round"
                      strokeLinejoin="round"
                    />
                  </svg>
                  <div className="flex text-sm text-gray-600">
                    <label
                      htmlFor="file-upload"
                      className="relative cursor-pointer bg-white rounded-md font-medium text-primary-600 hover:text-primary-500 focus-within:outline-none focus-within:ring-2 focus-within:ring-offset-2 focus-within:ring-primary-500"
                    >
                      <span>Upload a file</span>
                      <input
                        id="file-upload"
                        name="file-upload"
                        type="file"
                        className="sr-only"
                        onChange={handleFileChange}
                        accept=".pdf,.doc,.docx,.txt"
                      />
                    </label>
                    <p className="pl-1">or drag and drop</p>
                  </div>
                  <p className="text-xs text-gray-500">PDF, DOC, DOCX up to 10MB</p>
                  {file && (
                    <p className="text-sm text-primary-600 mt-2">
                      Selected file: {file.name} ({(file.size / 1024 / 1024).toFixed(2)} MB)
                    </p>
                  )}
                </div>
              </div>
            </div>

            {/* Signers Section */}
            <div className="border-t border-gray-200 pt-4">
              <h3 className="text-lg font-medium text-gray-900">Document Signers</h3>
              <p className="mt-1 text-sm text-gray-500">
                Add people who need to sign this document. They will receive an email with instructions.
              </p>

              {/* Existing Signers */}
              {signers.length > 0 && (
                <div className="mt-4">
                  <h4 className="text-sm font-medium text-gray-700">Current Signers</h4>
                  <ul className="mt-2 divide-y divide-gray-200 border border-gray-200 rounded-md">
                    {signers.map((signer, index) => (
                      <li key={index} className="px-4 py-3 flex items-center justify-between">
                        <div>
                          <p className="text-sm font-medium text-gray-900">{signer.name}</p>
                          <p className="text-sm text-gray-500">{signer.email}</p>
                          {signer.role && <p className="text-xs text-gray-500">Role: {signer.role}</p>}
                        </div>
                        <div className="flex items-center">
                          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                            Order: {signer.order}
                          </span>
                          <button
                            type="button"
                            onClick={() => handleRemoveSigner(index)}
                            className="ml-2 text-red-600 hover:text-red-900"
                          >
                            <svg className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                              <path
                                strokeLinecap="round"
                                strokeLinejoin="round"
                                strokeWidth={2}
                                d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"
                              />
                            </svg>
                          </button>
                        </div>
                      </li>
                    ))}
                  </ul>
                </div>
              )}

              {/* Add New Signer */}
              <div className="mt-4 grid grid-cols-1 gap-4 sm:grid-cols-3">
                <div>
                  <label htmlFor="signerEmail" className="block text-sm font-medium text-gray-700">
                    Email
                  </label>
                  <input
                    type="email"
                    id="signerEmail"
                    value={newSigner.email}
                    onChange={(e) => setNewSigner({ ...newSigner, email: e.target.value })}
                    className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
                  />
                </div>
                <div>
                  <label htmlFor="signerName" className="block text-sm font-medium text-gray-700">
                    Name
                  </label>
                  <input
                    type="text"
                    id="signerName"
                    value={newSigner.name}
                    onChange={(e) => setNewSigner({ ...newSigner, name: e.target.value })}
                    className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
                  />
                </div>
                <div>
                  <label htmlFor="signerRole" className="block text-sm font-medium text-gray-700">
                    Role (Optional)
                  </label>
                  <input
                    type="text"
                    id="signerRole"
                    value={newSigner.role}
                    onChange={(e) => setNewSigner({ ...newSigner, role: e.target.value })}
                    className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
                  />
                </div>
              </div>
              <button
                type="button"
                onClick={handleAddSigner}
                className="mt-2 inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md shadow-sm text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
              >
                Add Signer
              </button>
            </div>
          </div>
        </div>
        <div className="px-4 py-3 bg-gray-50 text-right sm:px-6">
          <button
            type="submit"
            disabled={loading}
            className="inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50"
          >
            {loading ? 'Saving...' : isEditMode ? 'Update Document' : 'Create Document'}
          </button>
        </div>
      </form>
    </Layout>
  );
};

export default SignableDocumentForm;