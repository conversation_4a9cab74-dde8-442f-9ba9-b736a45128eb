import { useState, useEffect, useContext } from 'react';
import { useParams, useNavigate, Link } from 'react-router-dom';
import axios from 'axios';
import { AuthContext } from '../../context/AuthContext';
import { useFarm } from '../../context/FarmContext';
import Layout from '../../components/Layout';
import { API_URL } from '../../config';

interface Template {
  id: string;
  title: string;
  description: string;
  document_type: string;
  file_path: string;
  file_size: number;
  file_type: string;
  mime_type: string;
  version: number;
  created_by: string;
  created_at: string;
  updated_at: string;
  fields: Field[];
  creator: {
    id: string;
    first_name: string;
    last_name: string;
    email: string;
  };
}

interface Field {
  id: string;
  field_type: string;
  field_name: string;
  field_value: string | null;
  is_required: boolean;
  page_number: number;
  x_position: number;
  y_position: number;
  width: number;
  height: number;
}

interface Signer {
  id?: string;
  email: string;
  name: string;
  role?: string;
  order?: number;
}

const CreateFromTemplate = () => {
  const { templateId } = useParams<{ templateId: string }>();
  
  const [template, setTemplate] = useState<Template | null>(null);
  const [title, setTitle] = useState('');
  const [description, setDescription] = useState('');
  const [signers, setSigners] = useState<Signer[]>([]);
  const [newSigner, setNewSigner] = useState<Signer>({ email: '', name: '', role: '' });
  
  const [loading, setLoading] = useState(true);
  const [submitting, setSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);

  const { user } = useContext(AuthContext);
  const { currentFarm } = useFarm();
  const navigate = useNavigate();

  // Fetch template details
  useEffect(() => {
    const fetchTemplate = async () => {
      setLoading(true);
      try {
        const response = await axios.get(`${API_URL}/document-signing/templates/${templateId}`, {
          headers: {
            Authorization: `Bearer ${localStorage.getItem('token')}`
          }
        });
        
        const templateData = response.data;
        setTemplate(templateData);
        setTitle(templateData.title);
        setDescription(templateData.description || '');
      } catch (err: any) {
        console.error('Error fetching template:', err);
        setError(err.response?.data?.error || 'Failed to load template. Please try again later.');
      } finally {
        setLoading(false);
      }
    };
    
    if (templateId) {
      fetchTemplate();
    }
  }, [templateId]);

  // Handle adding a new signer
  const handleAddSigner = () => {
    if (!newSigner.email || !newSigner.name) {
      setError('Signer email and name are required.');
      return;
    }
    
    setSigners([...signers, { ...newSigner, order: signers.length + 1 }]);
    setNewSigner({ email: '', name: '', role: '' });
  };

  // Handle removing a signer
  const handleRemoveSigner = (index: number) => {
    const updatedSigners = [...signers];
    updatedSigners.splice(index, 1);
    
    // Update order for remaining signers
    const reorderedSigners = updatedSigners.map((signer, idx) => ({
      ...signer,
      order: idx + 1
    }));
    
    setSigners(reorderedSigners);
  };

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!currentFarm?.id && !user?.farm_id) {
      setError('No farm selected. Please select a farm to create a document.');
      return;
    }
    
    setSubmitting(true);
    setError(null);
    
    try {
      const payload = {
        title,
        description,
        signers
      };
      
      const response = await axios.post(
        `${API_URL}/document-signing/templates/${templateId}/create-document`,
        payload,
        {
          headers: {
            'Content-Type': 'application/json',
            Authorization: `Bearer ${localStorage.getItem('token')}`
          }
        }
      );
      
      setSuccess('Document created successfully from template');
      
      // Navigate to document detail page after short delay
      setTimeout(() => {
        navigate(`/documents/signing/view/${response.data.document.id}`);
      }, 1500);
    } catch (err: any) {
      console.error('Error creating document from template:', err);
      setError(err.response?.data?.error || 'Failed to create document. Please try again later.');
    } finally {
      setSubmitting(false);
    }
  };

  return (
    <Layout>
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold text-gray-900">
          Create Document from Template
        </h1>
        <Link
          to="/documents/signing/templates"
          className="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md shadow-sm text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
        >
          Back to Templates
        </Link>
      </div>

      {/* Error message */}
      {error && (
        <div className="mb-4 p-4 text-sm text-red-700 bg-red-100 rounded-md">
          <span>{error}</span>
        </div>
      )}

      {/* Success message */}
      {success && (
        <div className="mb-4 p-4 text-sm text-green-700 bg-green-100 rounded-md">
          <span>{success}</span>
        </div>
      )}

      {/* Loading state */}
      {loading ? (
        <div className="flex flex-col justify-center items-center h-64 bg-gray-50 rounded-lg border border-gray-200">
          <p className="text-gray-600">Loading template...</p>
        </div>
      ) : template ? (
        <form onSubmit={handleSubmit} className="space-y-6 bg-white shadow sm:rounded-md sm:overflow-hidden">
          <div className="px-4 py-5 sm:p-6">
            <div className="grid grid-cols-1 gap-6">
              {/* Template Info */}
              <div className="bg-gray-50 p-4 rounded-md">
                <h3 className="text-md font-medium text-gray-700">Template Information</h3>
                <div className="mt-2 text-sm text-gray-500">
                  <p><span className="font-medium">Title:</span> {template.title}</p>
                  <p><span className="font-medium">Type:</span> {template.document_type}</p>
                  <p><span className="font-medium">Description:</span> {template.description || 'No description'}</p>
                </div>
              </div>

              {/* Document Title */}
              <div>
                <label htmlFor="title" className="block text-sm font-medium text-gray-700">
                  Document Title <span className="text-red-500">*</span>
                </label>
                <input
                  type="text"
                  id="title"
                  value={title}
                  onChange={(e) => setTitle(e.target.value)}
                  required
                  className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
                />
              </div>

              {/* Document Description */}
              <div>
                <label htmlFor="description" className="block text-sm font-medium text-gray-700">
                  Description
                </label>
                <textarea
                  id="description"
                  value={description}
                  onChange={(e) => setDescription(e.target.value)}
                  rows={3}
                  className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
                />
              </div>

              {/* Signers Section */}
              <div className="border-t border-gray-200 pt-4">
                <h3 className="text-lg font-medium text-gray-900">Document Signers</h3>
                <p className="mt-1 text-sm text-gray-500">
                  Add people who need to sign this document. They will receive an email with instructions.
                </p>

                {/* Existing Signers */}
                {signers.length > 0 && (
                  <div className="mt-4">
                    <h4 className="text-sm font-medium text-gray-700">Current Signers</h4>
                    <ul className="mt-2 divide-y divide-gray-200 border border-gray-200 rounded-md">
                      {signers.map((signer, index) => (
                        <li key={index} className="px-4 py-3 flex items-center justify-between">
                          <div>
                            <p className="text-sm font-medium text-gray-900">{signer.name}</p>
                            <p className="text-sm text-gray-500">{signer.email}</p>
                            {signer.role && <p className="text-xs text-gray-500">Role: {signer.role}</p>}
                          </div>
                          <div className="flex items-center">
                            <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                              Order: {signer.order}
                            </span>
                            <button
                              type="button"
                              onClick={() => handleRemoveSigner(index)}
                              className="ml-2 text-red-600 hover:text-red-900"
                            >
                              <svg className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path
                                  strokeLinecap="round"
                                  strokeLinejoin="round"
                                  strokeWidth={2}
                                  d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"
                                />
                              </svg>
                            </button>
                          </div>
                        </li>
                      ))}
                    </ul>
                  </div>
                )}

                {/* Add New Signer */}
                <div className="mt-4 grid grid-cols-1 gap-4 sm:grid-cols-3">
                  <div>
                    <label htmlFor="signerEmail" className="block text-sm font-medium text-gray-700">
                      Email
                    </label>
                    <input
                      type="email"
                      id="signerEmail"
                      value={newSigner.email}
                      onChange={(e) => setNewSigner({ ...newSigner, email: e.target.value })}
                      className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
                    />
                  </div>
                  <div>
                    <label htmlFor="signerName" className="block text-sm font-medium text-gray-700">
                      Name
                    </label>
                    <input
                      type="text"
                      id="signerName"
                      value={newSigner.name}
                      onChange={(e) => setNewSigner({ ...newSigner, name: e.target.value })}
                      className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
                    />
                  </div>
                  <div>
                    <label htmlFor="signerRole" className="block text-sm font-medium text-gray-700">
                      Role (Optional)
                    </label>
                    <input
                      type="text"
                      id="signerRole"
                      value={newSigner.role}
                      onChange={(e) => setNewSigner({ ...newSigner, role: e.target.value })}
                      className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
                    />
                  </div>
                </div>
                <button
                  type="button"
                  onClick={handleAddSigner}
                  className="mt-2 inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md shadow-sm text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
                >
                  Add Signer
                </button>
              </div>

              {/* Template Fields */}
              {template.fields && template.fields.length > 0 && (
                <div className="border-t border-gray-200 pt-4">
                  <h3 className="text-lg font-medium text-gray-900">Template Fields</h3>
                  <p className="mt-1 text-sm text-gray-500">
                    This template includes the following fields that will be added to your document.
                  </p>
                  <ul className="mt-2 divide-y divide-gray-200 border border-gray-200 rounded-md">
                    {template.fields.map((field) => (
                      <li key={field.id} className="px-4 py-3">
                        <div className="flex items-center justify-between">
                          <div>
                            <p className="text-sm font-medium text-gray-900">{field.field_name}</p>
                            <p className="text-xs text-gray-500">Type: {field.field_type}</p>
                          </div>
                          <div>
                            {field.is_required && (
                              <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                Required
                              </span>
                            )}
                          </div>
                        </div>
                      </li>
                    ))}
                  </ul>
                </div>
              )}
            </div>
          </div>
          <div className="px-4 py-3 bg-gray-50 text-right sm:px-6">
            <button
              type="submit"
              disabled={submitting}
              className="inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50"
            >
              {submitting ? 'Creating...' : 'Create Document'}
            </button>
          </div>
        </form>
      ) : (
        <div className="bg-white p-6 rounded-lg shadow">
          <p className="text-red-600">Template not found or you don't have access to it.</p>
        </div>
      )}
    </Layout>
  );
};

export default CreateFromTemplate;