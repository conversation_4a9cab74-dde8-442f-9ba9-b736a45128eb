import { useState, useEffect } from 'react';
import { use<PERSON><PERSON><PERSON>, <PERSON>, useNavigate } from 'react-router-dom';
import axios from 'axios';
import Layout from '../../components/Layout';
import { API_URL } from '../../config';
import { getSoilSampleData, SoilDataResponse, calculateSoilHealthScore, getCropRecommendations } from '../../services/soilDataService';
import { AreaChart, Area, XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer, LineChart, Line } from 'recharts';
import { useAuth } from '../../context/AuthContext';
import { useFarm } from '../../context/FarmContext';
import { getAuthToken } from '../utils/storageUtils';

interface SoilSample {
  id: string;
  farm_id: string;
  farm_name: string;
  field_id: string | null;
  field_name: string | null;
  sample_date: string;
  location: string | null;
  depth: string | null;
  lab_name: string | null;
  lab_reference: string | null;
  status: string;
  notes: string | null;
  created_at: string;
  updated_at: string;
}

interface SoilTestResult {
  id: string;
  soil_sample_id: string;
  ph: number | null;
  organic_matter: number | null;
  nitrogen: number | null;
  phosphorus: number | null;
  potassium: number | null;
  calcium: number | null;
  magnesium: number | null;
  sulfur: number | null;
  zinc: number | null;
  manganese: number | null;
  copper: number | null;
  iron: number | null;
  boron: number | null;
  cec: number | null;
  base_saturation: number | null;
  other_results: any;
  created_at: string;
  updated_at: string;
}

interface SoilRecommendation {
  id: string;
  soil_sample_id: string;
  field_id: string | null;
  nutrient_type: string;
  recommended_amount: number;
  unit: string;
  application_method: string | null;
  application_timing: string | null;
  notes: string | null;
  created_at: string;
  updated_at: string;
}

interface SoilHealthAnalysis {
  id: string;
  farm_id: string;
  field_id?: string;
  soil_data?: any;
  nutrient_levels_assessment: string;
  ph_assessment: string;
  organic_matter_assessment: string;
  identified_issues: string[];
  improvement_recommendations: string[];
  confidence_score: number;
  is_applied: boolean;
  created_at: string;
  updated_at: string;
}

const SoilDetail = () => {
  const { sampleId } = useParams<{ sampleId: string }>();
  const navigate = useNavigate();
  const { user } = useAuth();
  const { currentFarm } = useFarm();
  const [soilSample, setSoilSample] = useState<SoilSample | null>(null);
  const [testResults, setTestResults] = useState<SoilTestResult[]>([]);
  const [recommendations, setRecommendations] = useState<SoilRecommendation[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [deleteModalOpen, setDeleteModalOpen] = useState(false);

  // New state for detailed soil data
  const [detailedSoilData, setDetailedSoilData] = useState<SoilDataResponse | null>(null);
  const [soilDataLoading, setSoilDataLoading] = useState(false);
  const [soilDataError, setSoilDataError] = useState<string | null>(null);
  const [soilHealthScore, setSoilHealthScore] = useState<number | null>(null);
  const [cropRecommendations, setCropRecommendations] = useState<{ crop: string; suitability: number; notes: string }[]>([]);

  // AI Analysis state
  const [analyses, setAnalyses] = useState<SoilHealthAnalysis[]>([]);
  const [analysisLoading, setAnalysisLoading] = useState<boolean>(false);
  const [analysisError, setAnalysisError] = useState<string | null>(null);

  // Fetch soil sample data
  useEffect(() => {
    const fetchSoilSample = async () => {
      setLoading(true);
      setError(null);

      try {
        const response = await axios.get(`${API_URL}/soil/samples/${sampleId}`);
        setSoilSample(response.data.soilSample);

        // Fetch test results for this sample
        const resultsResponse = await axios.get(`${API_URL}/soil/test-results/sample/${sampleId}`);
        setTestResults(resultsResponse.data.soilTestResults || []);

        // Fetch recommendations for this sample
        const recommendationsResponse = await axios.get(`${API_URL}/soil/recommendations/sample/${sampleId}`);
        setRecommendations(recommendationsResponse.data.soilRecommendations || []);
      } catch (err: any) {
        console.error('Error fetching soil sample:', err);
        setError('Failed to load soil sample data. Please try again later.');
      } finally {
        setLoading(false);
      }
    };

    if (sampleId) {
      fetchSoilSample();
    }
  }, [sampleId]);

  // Fetch detailed soil data
  useEffect(() => {
    const fetchDetailedSoilData = async () => {
      if (!sampleId || !soilSample) return;

      setSoilDataLoading(true);
      setSoilDataError(null);

      try {
        // Fetch detailed soil data from our new API
        const detailedData = await getSoilSampleData(sampleId);
        setDetailedSoilData(detailedData);

        // Calculate soil health score
        if (detailedData.soilData) {
          const score = calculateSoilHealthScore(detailedData.soilData);
          setSoilHealthScore(score);

          // Get crop recommendations
          const recommendations = getCropRecommendations(detailedData.soilData);
          setCropRecommendations(recommendations);
        }
      } catch (err: any) {
        console.error('Error fetching detailed soil data:', err);
        setSoilDataError('Failed to load detailed soil information. Please try again later.');
      } finally {
        setSoilDataLoading(false);
      }
    };

    fetchDetailedSoilData();
  }, [sampleId, soilSample]);

  // Fetch soil health analyses
  useEffect(() => {
    const fetchSoilHealthAnalyses = async () => {
      if (!currentFarm || !soilSample) return;

      setAnalysisLoading(true);
      setAnalysisError(null);

      try {
        // Fetch soil health analyses for the farm
        const response = await axios.get(`${API_URL}/ai-analysis/soil-health/${currentFarm.id}/all`, {
          headers: {
            Authorization: `Bearer ${getAuthToken()}`
          }
        });

        if (response.data.success) {
          // Filter analyses for the current field if field_id is available
          const allAnalyses = response.data.analyses || [];
          if (soilSample.field_id) {
            const fieldAnalyses = allAnalyses.filter(
              (analysis: SoilHealthAnalysis) => analysis.field_id === soilSample.field_id
            );
            setAnalyses(fieldAnalyses);
          } else {
            setAnalyses(allAnalyses);
          }
        } else {
          throw new Error(response.data.message || 'Failed to fetch soil health analyses');
        }
      } catch (err: any) {
        console.error('Error fetching soil health analyses:', err);
        setAnalysisError('Failed to load soil health analyses. Please try again later.');

        // Fallback to empty array if API call fails
        setAnalyses([]);
      } finally {
        setAnalysisLoading(false);
      }
    };

    if (user && currentFarm && soilSample) {
      fetchSoilHealthAnalyses();
    }
  }, [user, currentFarm, soilSample]);

  // Generate soil health analysis
  const generateSoilHealthAnalysis = async () => {
    if (!currentFarm || !soilSample || !soilSample.field_id) {
      setAnalysisError('Cannot generate analysis: missing farm, sample, or field information');
      return;
    }

    try {
      setAnalysisLoading(true);

      // Send request to the backend to generate a new soil health analysis
      const response = await axios.post(`${API_URL}/ai-analysis/soil-health`, {
        farmId: currentFarm.id,
        fieldId: soilSample.field_id,
        soilData: {
          // Include any additional soil data that might be useful for the analysis
          testResults: testResults.length > 0 ? testResults[0] : null,
          notes: soilSample.notes
        }
      }, {
        headers: {
          Authorization: `Bearer ${getAuthToken()}`
        }
      });

      if (response.data.success) {
        // Add the new analysis to the beginning of the list
        setAnalyses([response.data.analysis, ...analyses]);
        setAnalysisError(null);
      } else {
        throw new Error(response.data.message || 'Failed to generate soil health analysis');
      }
    } catch (err: any) {
      console.error('Error generating soil health analysis:', err);
      setAnalysisError('Failed to generate soil health analysis. Please try again later.');
    } finally {
      setAnalysisLoading(false);
    }
  };

  // Apply soil health analysis
  const applySoilHealthAnalysis = async (analysisId: string) => {
    try {
      setAnalysisLoading(true);

      // Send request to apply the analysis
      const response = await axios.put(`${API_URL}/ai-analysis/soil-health/${analysisId}`, {
        isApplied: true
      }, {
        headers: {
          Authorization: `Bearer ${getAuthToken()}`
        }
      });

      if (response.data.success) {
        // Update the analyses list to reflect the change
        setAnalyses(analyses.map(analysis => 
          analysis.id === analysisId 
            ? { ...analysis, is_applied: true } 
            : analysis
        ));

        // You could also implement logic here to apply the recommendations
        // For example, creating soil amendment tasks or updating soil management plans

        setAnalysisError(null);
      } else {
        throw new Error(response.data.message || 'Failed to apply soil health analysis');
      }
    } catch (err: any) {
      console.error('Error applying soil health analysis:', err);
      setAnalysisError('Failed to apply soil health analysis. Please try again later.');
    } finally {
      setAnalysisLoading(false);
    }
  };

  // Handle delete
  const handleDelete = async () => {
    setLoading(true);
    setError(null);

    try {
      await axios.delete(`${API_URL}/soil/samples/${sampleId}`);
      navigate('/soil');
    } catch (err: any) {
      console.error('Error deleting soil sample:', err);
      setError('Failed to delete soil sample. Please try again later.');
      setLoading(false);
    }
  };

  // Format date for display
  const formatDate = (dateString: string | null) => {
    if (!dateString) return 'N/A';
    return new Date(dateString).toLocaleDateString();
  };

  // Get status badge
  const getStatusBadge = (status: string) => {
    let bgColor = 'bg-gray-100 text-gray-800';

    switch (status.toLowerCase()) {
      case 'pending':
        bgColor = 'bg-yellow-100 text-yellow-800';
        break;
      case 'received':
        bgColor = 'bg-blue-100 text-blue-800';
        break;
      case 'analyzed':
        bgColor = 'bg-green-100 text-green-800';
        break;
    }

    return (
      <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${bgColor}`}>
        {status.charAt(0).toUpperCase() + status.slice(1)}
      </span>
    );
  };

  if (loading) {
    return (
      <Layout>
        <div className="flex justify-center items-center py-12">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-500"></div>
        </div>
      </Layout>
    );
  }

  if (error || !soilSample) {
    return (
      <Layout>
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mb-6" role="alert">
          <span className="block sm:inline">{error || 'Soil sample not found'}</span>
        </div>
        <Link
          to="/soil"
          className="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md shadow-sm text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
        >
          Back to Soil Samples
        </Link>
      </Layout>
    );
  }

  return (
    <Layout>
      {/* Header */}
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold text-gray-900">
          {soilSample.field_name ? `${soilSample.field_name} Sample` : `Sample from ${formatDate(soilSample.sample_date)}`}
        </h1>
        <div className="flex space-x-2">
          <Link
            to="/soil"
            className="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md shadow-sm text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
          >
            Back to Soil Samples
          </Link>
          <Link
            to={`/soil/samples/${sampleId}/edit`}
            className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
          >
            Edit Sample
          </Link>
          <button
            onClick={() => setDeleteModalOpen(true)}
            className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
          >
            Delete
          </button>
        </div>
      </div>

      {/* Sample Details */}
      <div className="bg-white shadow overflow-hidden sm:rounded-lg mb-6">
        <div className="px-4 py-5 sm:px-6 flex justify-between items-center">
          <div>
            <h3 className="text-lg leading-6 font-medium text-gray-900">Soil Sample Details</h3>
            <p className="mt-1 max-w-2xl text-sm text-gray-500">Details about the soil sample.</p>
          </div>
          <div>
            {getStatusBadge(soilSample.status)}
          </div>
        </div>
        <div className="border-t border-gray-200">
          <dl>
            <div className="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
              <dt className="text-sm font-medium text-gray-500">Farm</dt>
              <dd className="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">{soilSample.farm_name}</dd>
            </div>
            <div className="bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
              <dt className="text-sm font-medium text-gray-500">Field</dt>
              <dd className="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">{soilSample.field_name || 'N/A'}</dd>
            </div>
            <div className="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
              <dt className="text-sm font-medium text-gray-500">Sample Date</dt>
              <dd className="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">{formatDate(soilSample.sample_date)}</dd>
            </div>
            <div className="bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
              <dt className="text-sm font-medium text-gray-500">Location</dt>
              <dd className="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">{soilSample.location || 'N/A'}</dd>
            </div>
            <div className="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
              <dt className="text-sm font-medium text-gray-500">Depth</dt>
              <dd className="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">{soilSample.depth || 'N/A'}</dd>
            </div>
            <div className="bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
              <dt className="text-sm font-medium text-gray-500">Lab</dt>
              <dd className="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">{soilSample.lab_name || 'N/A'}</dd>
            </div>
            <div className="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
              <dt className="text-sm font-medium text-gray-500">Lab Reference</dt>
              <dd className="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">{soilSample.lab_reference || 'N/A'}</dd>
            </div>
            <div className="bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
              <dt className="text-sm font-medium text-gray-500">Notes</dt>
              <dd className="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2 whitespace-pre-line">
                {soilSample.notes || 'No notes available'}
              </dd>
            </div>
            <div className="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
              <dt className="text-sm font-medium text-gray-500">Created</dt>
              <dd className="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">{formatDate(soilSample.created_at)}</dd>
            </div>
            <div className="bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
              <dt className="text-sm font-medium text-gray-500">Last Updated</dt>
              <dd className="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">{formatDate(soilSample.updated_at)}</dd>
            </div>
          </dl>
        </div>
      </div>

      {/* AI Soil Health Analysis */}
      <div className="bg-white shadow overflow-hidden sm:rounded-lg mb-6">
        <div className="px-4 py-5 sm:px-6 flex justify-between items-center">
          <div>
            <h3 className="text-lg leading-6 font-medium text-gray-900">AI-Driven Soil Health Analysis</h3>
            <p className="mt-1 max-w-2xl text-sm text-gray-500">Advanced analysis and recommendations powered by AI.</p>
          </div>
          <div>
            <button
              type="button"
              onClick={generateSoilHealthAnalysis}
              disabled={analysisLoading || !soilSample?.field_id}
              className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {analysisLoading ? 'Generating...' : 'Generate AI Analysis'}
            </button>
          </div>
        </div>

        {analysisError && (
          <div className="border-t border-gray-200 px-4 py-5">
            <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative" role="alert">
              <span className="block sm:inline">{analysisError}</span>
            </div>
          </div>
        )}

        {analyses.length === 0 ? (
          <div className="border-t border-gray-200 px-4 py-5 text-center text-sm text-gray-500">
            No AI analyses available. Generate a new analysis to get detailed insights.
          </div>
        ) : (
          <div className="border-t border-gray-200">
            <div className="px-4 py-5">
              <div className="space-y-6">
                {analyses.map((analysis) => (
                  <div key={analysis.id} className="bg-white shadow overflow-hidden sm:rounded-lg border border-gray-200">
                    <div className="px-4 py-5 sm:px-6">
                      <h3 className="text-lg leading-6 font-medium text-gray-900">
                        Soil Health Analysis
                        {analysis.field_id && soilSample?.field_name && 
                          ` - ${soilSample.field_name}`}
                      </h3>
                      <p className="mt-1 max-w-2xl text-sm text-gray-500">
                        Created: {new Date(analysis.created_at).toLocaleDateString()}
                        <span className="ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                          AI Confidence: {Math.round(analysis.confidence_score)}%
                        </span>
                      </p>
                    </div>
                    <div className="border-t border-gray-200 px-4 py-5 sm:p-0">
                      <dl className="sm:divide-y sm:divide-gray-200">
                        <div className="py-4 sm:py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                          <dt className="text-sm font-medium text-gray-500">Nutrient Levels Assessment</dt>
                          <dd className="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                            {analysis.nutrient_levels_assessment}
                          </dd>
                        </div>
                        <div className="py-4 sm:py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                          <dt className="text-sm font-medium text-gray-500">pH Assessment</dt>
                          <dd className="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                            {analysis.ph_assessment}
                          </dd>
                        </div>
                        <div className="py-4 sm:py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                          <dt className="text-sm font-medium text-gray-500">Organic Matter Assessment</dt>
                          <dd className="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                            {analysis.organic_matter_assessment}
                          </dd>
                        </div>
                        <div className="py-4 sm:py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                          <dt className="text-sm font-medium text-gray-500">Identified Issues</dt>
                          <dd className="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                            <ul className="list-disc pl-5 space-y-1">
                              {analysis.identified_issues.map((issue, index) => (
                                <li key={index}>{issue}</li>
                              ))}
                            </ul>
                          </dd>
                        </div>
                        <div className="py-4 sm:py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                          <dt className="text-sm font-medium text-gray-500">Improvement Recommendations</dt>
                          <dd className="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                            <ul className="list-disc pl-5 space-y-1">
                              {analysis.improvement_recommendations.map((recommendation, index) => (
                                <li key={index}>{recommendation}</li>
                              ))}
                            </ul>
                          </dd>
                        </div>
                      </dl>
                    </div>
                    <div className="px-4 py-3 bg-gray-50 text-right sm:px-6">
                      {!analysis.is_applied && (
                        <button
                          type="button"
                          onClick={() => applySoilHealthAnalysis(analysis.id)}
                          disabled={analysisLoading}
                          className="inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
                        >
                          Apply Recommendations
                        </button>
                      )}
                      {analysis.is_applied && (
                        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                          Applied
                        </span>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Detailed Soil Information */}
      <div className="bg-white shadow overflow-hidden sm:rounded-lg mb-6">
        <div className="px-4 py-5 sm:px-6">
          <h3 className="text-lg leading-6 font-medium text-gray-900">Detailed Soil Information</h3>
          <p className="mt-1 max-w-2xl text-sm text-gray-500">Comprehensive soil analysis and recommendations.</p>
        </div>

        {soilDataLoading ? (
          <div className="border-t border-gray-200 px-4 py-12 flex justify-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-500"></div>
          </div>
        ) : soilDataError ? (
          <div className="border-t border-gray-200 px-4 py-5">
            <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative" role="alert">
              <span className="block sm:inline">{soilDataError}</span>
            </div>
          </div>
        ) : detailedSoilData ? (
          <div className="border-t border-gray-200">
            {/* Soil Health Score */}
            <div className="px-4 py-5">
              <h4 className="text-lg font-medium text-gray-900 mb-4">Soil Health Score</h4>
              <div className="flex items-center mb-4">
                <div className={`text-3xl font-bold rounded-full w-20 h-20 flex items-center justify-center ${
                  soilHealthScore && soilHealthScore >= 80 ? 'bg-green-100 text-green-800' :
                  soilHealthScore && soilHealthScore >= 60 ? 'bg-yellow-100 text-yellow-800' :
                  'bg-red-100 text-red-800'
                }`}>
                  {soilHealthScore || 'N/A'}
                </div>
                <div className="ml-4">
                  <p className="text-sm text-gray-500">
                    {soilHealthScore && soilHealthScore >= 80 ? 'Excellent soil health. Your soil is in great condition for most crops.' :
                     soilHealthScore && soilHealthScore >= 60 ? 'Good soil health. Some improvements could enhance productivity.' :
                     soilHealthScore && soilHealthScore >= 40 ? 'Fair soil health. Consider soil amendments to improve quality.' :
                     soilHealthScore ? 'Poor soil health. Significant improvements needed.' : 'Unable to calculate soil health score.'}
                  </p>
                </div>
              </div>

              {/* Soil Composition Chart */}
              <h4 className="text-md font-medium text-gray-900 mb-2">Soil Composition</h4>
              <div className="h-64 mb-6">
                {detailedSoilData.soilData.soilProperties.clayContent !== null && 
                 detailedSoilData.soilData.soilProperties.sandContent !== null && 
                 detailedSoilData.soilData.soilProperties.siltContent !== null ? (
                  <ResponsiveContainer width="100%" height="100%">
                    <LineChart
                      data={[
                        { name: 'Clay', value: detailedSoilData.soilData.soilProperties.clayContent },
                        { name: 'Sand', value: detailedSoilData.soilData.soilProperties.sandContent },
                        { name: 'Silt', value: detailedSoilData.soilData.soilProperties.siltContent }
                      ]}
                      margin={{
                        top: 5,
                        right: 30,
                        left: 20,
                        bottom: 5,
                      }}
                    >
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="name" />
                      <YAxis />
                      <Tooltip formatter={(value) => `${value}%`} />
                      <Legend />
                      <Line type="monotone" dataKey="value" stroke="#8884d8" activeDot={{ r: 8 }} name="Percentage" />
                    </LineChart>
                  </ResponsiveContainer>
                ) : (
                  <p className="text-sm text-gray-500 py-4">Soil composition data not available.</p>
                )}
              </div>

              {/* Soil Properties */}
              <h4 className="text-md font-medium text-gray-900 mb-2">Soil Properties</h4>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                <div className="bg-gray-50 p-4 rounded">
                  <p className="text-sm font-medium text-gray-500">Soil Type</p>
                  <p className="text-md text-gray-900">{detailedSoilData.soilData.soilType || 'Unknown'}</p>
                </div>
                <div className="bg-gray-50 p-4 rounded">
                  <p className="text-sm font-medium text-gray-500">Soil Series</p>
                  <p className="text-md text-gray-900">{detailedSoilData.soilData.soilSeries || 'Unknown'}</p>
                </div>
                <div className="bg-gray-50 p-4 rounded">
                  <p className="text-sm font-medium text-gray-500">pH</p>
                  <p className="text-md text-gray-900">{detailedSoilData.soilData.soilProperties.ph || 'N/A'}</p>
                </div>
                <div className="bg-gray-50 p-4 rounded">
                  <p className="text-sm font-medium text-gray-500">Organic Matter (%)</p>
                  <p className="text-md text-gray-900">{detailedSoilData.soilData.soilProperties.organicMatter || 'N/A'}</p>
                </div>
                <div className="bg-gray-50 p-4 rounded">
                  <p className="text-sm font-medium text-gray-500">CEC (meq/100g)</p>
                  <p className="text-md text-gray-900">{detailedSoilData.soilData.soilProperties.cec || 'N/A'}</p>
                </div>
                <div className="bg-gray-50 p-4 rounded">
                  <p className="text-sm font-medium text-gray-500">Drainage Class</p>
                  <p className="text-md text-gray-900">{detailedSoilData.soilData.soilProperties.drainageClass || 'Unknown'}</p>
                </div>
              </div>

              {/* Rainfall Data */}
              <h4 className="text-md font-medium text-gray-900 mb-2">Rainfall Data</h4>
              <div className="h-64 mb-6">
                {detailedSoilData.soilData.rainfall && detailedSoilData.soilData.rainfall.forecast ? (
                  <ResponsiveContainer width="100%" height="100%">
                    <AreaChart
                      data={detailedSoilData.soilData.rainfall.forecast}
                      margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
                    >
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="date" tickFormatter={(date) => new Date(date).toLocaleDateString('en-US', { weekday: 'short' })} />
                      <YAxis yAxisId="left" label={{ value: 'Precipitation (in)', angle: -90, position: 'insideLeft' }} />
                      <YAxis yAxisId="right" orientation="right" label={{ value: 'Probability (%)', angle: 90, position: 'insideRight' }} />
                      <Tooltip formatter={(value, name) => [value, name === 'precipitation' ? 'Precipitation (in)' : 'Probability (%)']} />
                      <Legend />
                      <Area yAxisId="left" dataKey="precipitation" name="Precipitation" fill="#8884d8" stroke="#8884d8" />
                      <Area yAxisId="right" dataKey="probability" name="Probability" fill="#82ca9d" stroke="#82ca9d" />
                    </AreaChart>
                  </ResponsiveContainer>
                ) : (
                  <p className="text-sm text-gray-500 py-4">Rainfall forecast data not available.</p>
                )}
              </div>

              {/* Soil Limitations */}
              {detailedSoilData.soilData.soilLimitations && detailedSoilData.soilData.soilLimitations.length > 0 && (
                <div className="mb-6">
                  <h4 className="text-md font-medium text-gray-900 mb-2">Soil Limitations</h4>
                  <ul className="list-disc pl-5 text-sm text-gray-700">
                    {detailedSoilData.soilData.soilLimitations.map((limitation, index) => (
                      <li key={index} className="mb-1">{limitation}</li>
                    ))}
                  </ul>
                </div>
              )}

              {/* Crop Recommendations */}
              <h4 className="text-md font-medium text-gray-900 mb-2">Crop Suitability</h4>
              <div className="h-64 mb-6">
                {cropRecommendations.length > 0 ? (
                  <ResponsiveContainer width="100%" height="100%">
                    <AreaChart
                      data={cropRecommendations}
                      margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
                    >
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="crop" />
                      <YAxis domain={[0, 100]} />
                      <Tooltip formatter={(value) => [`${value}%`, 'Suitability']} />
                      <Legend />
                      <Area dataKey="suitability" name="Suitability Score" fill="#8884d8" stroke="#8884d8" />
                    </AreaChart>
                  </ResponsiveContainer>
                ) : (
                  <p className="text-sm text-gray-500 py-4">Crop suitability data not available.</p>
                )}
              </div>

              {/* Recommendations */}
              <h4 className="text-md font-medium text-gray-900 mb-2">Recommendations</h4>
              {detailedSoilData.recommendations && detailedSoilData.recommendations.length > 0 ? (
                <div className="space-y-4">
                  {detailedSoilData.recommendations.map((recommendation, index) => (
                    <div key={index} className={`p-4 rounded ${
                      recommendation.priority === 'High' ? 'bg-red-50 border-l-4 border-red-500' :
                      recommendation.priority === 'Medium' ? 'bg-yellow-50 border-l-4 border-yellow-500' :
                      'bg-green-50 border-l-4 border-green-500'
                    }`}>
                      <div className="flex justify-between">
                        <h5 className="text-sm font-medium text-gray-900">{recommendation.type}: {recommendation.issue}</h5>
                        <span className={`text-xs font-medium px-2 py-1 rounded-full ${
                          recommendation.priority === 'High' ? 'bg-red-100 text-red-800' :
                          recommendation.priority === 'Medium' ? 'bg-yellow-100 text-yellow-800' :
                          'bg-green-100 text-green-800'
                        }`}>
                          {recommendation.priority} Priority
                        </span>
                      </div>
                      <p className="text-sm text-gray-700 mt-1">{recommendation.recommendation}</p>
                    </div>
                  ))}
                </div>
              ) : (
                <p className="text-sm text-gray-500 py-4">No recommendations available.</p>
              )}
            </div>
          </div>
        ) : (
          <div className="border-t border-gray-200 px-4 py-5">
            <p className="text-sm text-gray-500">No detailed soil information available.</p>
          </div>
        )}
      </div>

      {/* Test Results */}
      <div className="bg-white shadow overflow-hidden sm:rounded-lg mb-6">
        <div className="px-4 py-5 sm:px-6 flex justify-between items-center">
          <div>
            <h3 className="text-lg leading-6 font-medium text-gray-900">Soil Test Results</h3>
            <p className="mt-1 max-w-2xl text-sm text-gray-500">Analysis results for this soil sample.</p>
          </div>
          <div className="flex space-x-2">
            <Link
              to={`/soil/test-results/new?sampleId=${sampleId}`}
              className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
            >
              Add Test Results
            </Link>
            <Link
              to={`/soil/test-results/import/${sampleId}`}
              className="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md shadow-sm text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
            >
              Import Lab Results
            </Link>
          </div>
        </div>

        {testResults.length === 0 ? (
          <div className="border-t border-gray-200 px-4 py-5 text-center text-sm text-gray-500">
            No test results available for this sample.
          </div>
        ) : (
          <div className="border-t border-gray-200">
            {testResults.map((result, index) => (
              <div key={result.id} className={index > 0 ? "border-t border-gray-200" : ""}>
                <dl>
                  <div className="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                    <dt className="text-sm font-medium text-gray-500">pH</dt>
                    <dd className="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">{result.ph || 'N/A'}</dd>
                  </div>
                  <div className="bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                    <dt className="text-sm font-medium text-gray-500">Organic Matter (%)</dt>
                    <dd className="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">{result.organic_matter || 'N/A'}</dd>
                  </div>
                  <div className="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                    <dt className="text-sm font-medium text-gray-500">Nitrogen (ppm)</dt>
                    <dd className="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">{result.nitrogen || 'N/A'}</dd>
                  </div>
                  <div className="bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                    <dt className="text-sm font-medium text-gray-500">Phosphorus (ppm)</dt>
                    <dd className="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">{result.phosphorus || 'N/A'}</dd>
                  </div>
                  <div className="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                    <dt className="text-sm font-medium text-gray-500">Potassium (ppm)</dt>
                    <dd className="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">{result.potassium || 'N/A'}</dd>
                  </div>
                  <div className="bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                    <dt className="text-sm font-medium text-gray-500">Calcium (ppm)</dt>
                    <dd className="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">{result.calcium || 'N/A'}</dd>
                  </div>
                  <div className="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                    <dt className="text-sm font-medium text-gray-500">Magnesium (ppm)</dt>
                    <dd className="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">{result.magnesium || 'N/A'}</dd>
                  </div>
                  <div className="bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                    <dt className="text-sm font-medium text-gray-500">CEC (meq/100g)</dt>
                    <dd className="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">{result.cec || 'N/A'}</dd>
                  </div>
                  <div className="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                    <dt className="text-sm font-medium text-gray-500">Base Saturation (%)</dt>
                    <dd className="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">{result.base_saturation || 'N/A'}</dd>
                  </div>
                  <div className="bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                    <dt className="text-sm font-medium text-gray-500">Date Added</dt>
                    <dd className="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">{formatDate(result.created_at)}</dd>
                  </div>
                  <div className="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                    <dt className="text-sm font-medium text-gray-500">Actions</dt>
                    <dd className="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                      <Link
                        to={`/soil/test-results/${result.id}/edit`}
                        className="text-primary-600 hover:text-primary-900 mr-4"
                      >
                        Edit
                      </Link>
                      <button
                        onClick={async () => {
                          if (window.confirm('Are you sure you want to delete these test results?')) {
                            try {
                              await axios.delete(`${API_URL}/soil/test-results/${result.id}`);
                              setTestResults(testResults.filter(r => r.id !== result.id));
                            } catch (err) {
                              console.error('Error deleting test results:', err);
                              alert('Failed to delete test results. Please try again later.');
                            }
                          }
                        }}
                        className="text-red-600 hover:text-red-900"
                      >
                        Delete
                      </button>
                    </dd>
                  </div>
                </dl>
              </div>
            ))}
          </div>
        )}
      </div>

      {/* Soil Recommendations */}
      <div className="bg-white shadow overflow-hidden sm:rounded-lg mb-6">
        <div className="px-4 py-5 sm:px-6 flex justify-between items-center">
          <div>
            <h3 className="text-lg leading-6 font-medium text-gray-900">Soil Recommendations</h3>
            <p className="mt-1 max-w-2xl text-sm text-gray-500">Specific soil needs and recommendations.</p>
          </div>
          <div className="flex space-x-2">
            <Link
              to={`/soil/recommendations/new?sampleId=${sampleId}`}
              className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
            >
              Add Recommendation
            </Link>
          </div>
        </div>

        {recommendations.length === 0 ? (
          <div className="border-t border-gray-200 px-4 py-5 text-center text-sm text-gray-500">
            No recommendations available for this sample.
          </div>
        ) : (
          <div className="border-t border-gray-200">
            {recommendations.map((recommendation, index) => (
              <div key={recommendation.id} className={index > 0 ? "border-t border-gray-200" : ""}>
                <dl>
                  <div className="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                    <dt className="text-sm font-medium text-gray-500">Nutrient Type</dt>
                    <dd className="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                      {recommendation.nutrient_type.charAt(0).toUpperCase() + recommendation.nutrient_type.slice(1)}
                    </dd>
                  </div>
                  <div className="bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                    <dt className="text-sm font-medium text-gray-500">Recommended Amount</dt>
                    <dd className="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                      {recommendation.recommended_amount} {recommendation.unit}
                    </dd>
                  </div>
                  {recommendation.application_method && (
                    <div className="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                      <dt className="text-sm font-medium text-gray-500">Application Method</dt>
                      <dd className="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                        {recommendation.application_method}
                      </dd>
                    </div>
                  )}
                  {recommendation.application_timing && (
                    <div className="bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                      <dt className="text-sm font-medium text-gray-500">Application Timing</dt>
                      <dd className="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                        {recommendation.application_timing}
                      </dd>
                    </div>
                  )}
                  {recommendation.notes && (
                    <div className="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                      <dt className="text-sm font-medium text-gray-500">Notes</dt>
                      <dd className="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2 whitespace-pre-line">
                        {recommendation.notes}
                      </dd>
                    </div>
                  )}
                  <div className="bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                    <dt className="text-sm font-medium text-gray-500">Date Added</dt>
                    <dd className="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                      {formatDate(recommendation.created_at)}
                    </dd>
                  </div>
                  <div className="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                    <dt className="text-sm font-medium text-gray-500">Actions</dt>
                    <dd className="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                      <Link
                        to={`/soil/recommendations/${recommendation.id}/edit`}
                        className="text-primary-600 hover:text-primary-900 mr-4"
                      >
                        Edit
                      </Link>
                      <button
                        onClick={async () => {
                          if (window.confirm('Are you sure you want to delete this recommendation?')) {
                            try {
                              await axios.delete(`${API_URL}/soil/recommendations/${recommendation.id}`);
                              setRecommendations(recommendations.filter(r => r.id !== recommendation.id));
                            } catch (err) {
                              console.error('Error deleting recommendation:', err);
                              alert('Failed to delete recommendation. Please try again later.');
                            }
                          }
                        }}
                        className="text-red-600 hover:text-red-900"
                      >
                        Delete
                      </button>
                    </dd>
                  </div>
                </dl>
              </div>
            ))}
          </div>
        )}
      </div>

      {/* Delete Confirmation Modal */}
      {deleteModalOpen && (
        <div className="fixed z-10 inset-0 overflow-y-auto">
          <div className="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
            <div className="fixed inset-0 transition-opacity" aria-hidden="true">
              <div className="absolute inset-0 bg-gray-500 opacity-75"></div>
            </div>
            <span className="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>
            <div className="inline-block align-bottom bg-white rounded-lg text-left overflow-y-auto max-h-[90vh] shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
              <div className="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                <div className="sm:flex sm:items-start">
                  <div className="mx-auto flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-full bg-red-100 sm:mx-0 sm:h-10 sm:w-10">
                    <svg className="h-6 w-6 text-red-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                    </svg>
                  </div>
                  <div className="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left">
                    <h3 className="text-lg leading-6 font-medium text-gray-900" id="modal-title">
                      Delete Soil Sample
                    </h3>
                    <div className="mt-2">
                      <p className="text-sm text-gray-500">
                        Are you sure you want to delete this soil sample? This action cannot be undone.
                        {testResults.length > 0 && (
                          <span className="block mt-2 font-semibold">
                            Warning: This will also delete all associated test results.
                          </span>
                        )}
                      </p>
                    </div>
                  </div>
                </div>
              </div>
              <div className="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
                <button
                  type="button"
                  className="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-red-600 text-base font-medium text-white hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 sm:ml-3 sm:w-auto sm:text-sm"
                  onClick={handleDelete}
                >
                  Delete
                </button>
                <button
                  type="button"
                  className="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm"
                  onClick={() => setDeleteModalOpen(false)}
                >
                  Cancel
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </Layout>
  );
};

export default SoilDetail;
