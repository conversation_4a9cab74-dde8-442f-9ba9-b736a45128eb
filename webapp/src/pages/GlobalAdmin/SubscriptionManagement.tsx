import React, { useState, useEffect } from 'react';
import axios from 'axios';
import { API_URL } from '../../config';
import { getDefaultMenuStructure } from '../../utils/menuUtils';
import { getAuthToken } from '../utils/storageUtils';

interface SubscriptionPlan {
  id: string;
  name: string;
  description: string;
  price_monthly: number;
  price_yearly: number;
  features: any;
  max_farms: number;
  max_users: number;
  is_active: boolean;
  is_trial: boolean;
  is_default: boolean;
  trial_duration_days: number;
  trial_features: any;
  requires_payment_method: boolean;
  created_at: string;
}

const SubscriptionManagement: React.FC = () => {
  const [subscriptionPlans, setSubscriptionPlans] = useState<SubscriptionPlan[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [showAddModal, setShowAddModal] = useState<boolean>(false);
  const [showEditModal, setShowEditModal] = useState<boolean>(false);
  const [currentPlan, setCurrentPlan] = useState<SubscriptionPlan | null>(null);
  const [billingCycle, setBillingCycle] = useState<'monthly' | 'yearly'>('monthly');
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    price_monthly: 0,
    price_yearly: 0,
    max_farms: 0,
    max_users: 0,
    is_active: true,
    is_trial: false,
    is_default: false,
    trial_duration_days: 14,
    requires_payment_method: false,
    features: {},
    trial_features: {}
  });
  const [availableFeatures, setAvailableFeatures] = useState<{id: string, title: string, category: string}[]>([]);
  const [selectedFeatures, setSelectedFeatures] = useState<{[key: string]: boolean}>({});

  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);

        // Fetch subscription plans from API
        const response = await axios.get(`${API_URL}/api/subscriptions/plans`, {
          headers: {
            Authorization: `Bearer ${getAuthToken()}`
          }
        });

        setSubscriptionPlans(response.data.plans || []);

        // Get all available menu items to use as features
        const menuPreferences = getDefaultMenuStructure();
        const features: {id: string, title: string, category: string}[] = [];

        // Add header items
        menuPreferences.headerItems.forEach(item => {
          features.push({
            id: item.id,
            title: item.title,
            category: 'Header Menu'
          });
        });

        // Add sidebar items
        menuPreferences.sidebarCategories.forEach(category => {
          category.items.forEach(item => {
            features.push({
              id: item.id,
              title: item.title,
              category: category.title
            });
          });
        });

        // Add quick links items
        menuPreferences.quickLinksItems.forEach(item => {
          features.push({
            id: item.id,
            title: item.title,
            category: 'Quick Links'
          });
        });

        setAvailableFeatures(features);
        setLoading(false);
      } catch (err) {
        setError('Failed to load subscription plans');
        setLoading(false);
      }
    };

    fetchData();
  }, []);

  const handleAddPlan = () => {
    setFormData({
      name: '',
      description: '',
      price_monthly: 0,
      price_yearly: 0,
      max_farms: 0,
      max_users: 0,
      is_active: true,
      is_trial: false,
      is_default: false,
      trial_duration_days: 14,
      requires_payment_method: false,
      features: {},
      trial_features: {}
    });
    setSelectedFeatures({});
    setShowAddModal(true);
  };

  const handleEditPlan = (plan: SubscriptionPlan) => {
    setCurrentPlan(plan);
    setFormData({
      name: plan.name,
      description: plan.description,
      price_monthly: plan.price_monthly,
      price_yearly: plan.price_yearly,
      max_farms: plan.max_farms,
      max_users: plan.max_users,
      is_active: plan.is_active,
      is_trial: plan.is_trial || false,
      is_default: plan.is_default || false,
      trial_duration_days: plan.trial_duration_days || 14,
      requires_payment_method: plan.requires_payment_method || false,
      features: plan.features,
      trial_features: plan.trial_features || {}
    });

    // Convert features object to selected features map
    const selectedFeaturesMap: {[key: string]: boolean} = {};

    // If plan.features is an object with feature IDs as keys
    if (plan.features && typeof plan.features === 'object') {
      Object.keys(plan.features).forEach(featureId => {
        selectedFeaturesMap[featureId] = plan.features[featureId] === true;
      });
    }

    setSelectedFeatures(selectedFeaturesMap);
    setShowEditModal(true);
  };

  const handleFormChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value, type } = e.target as HTMLInputElement;

    if (type === 'checkbox') {
      const { checked } = e.target as HTMLInputElement;
      setFormData(prev => ({ ...prev, [name]: checked }));
    } else if (type === 'number') {
      setFormData(prev => ({ ...prev, [name]: parseFloat(value) }));
    } else {
      setFormData(prev => ({ ...prev, [name]: value }));
    }
  };

  const handleFeatureToggle = (featureId: string) => {
    setSelectedFeatures(prev => ({
      ...prev,
      [featureId]: !prev[featureId]
    }));
  };

  const prepareFormDataWithFeatures = () => {
    // Convert selectedFeatures map to features object
    const features = Object.keys(selectedFeatures).reduce((acc, featureId) => {
      if (selectedFeatures[featureId]) {
        acc[featureId] = true;
      }
      return acc;
    }, {} as Record<string, boolean>);

    return {
      ...formData,
      features
    };
  };

  const handleSubmitAdd = async (e: React.FormEvent) => {
    e.preventDefault();
    const dataWithFeatures = prepareFormDataWithFeatures();

    try {
      // Make API call to create a plan
      const response = await axios.post(`${API_URL}/api/subscriptions/plans`, dataWithFeatures, {
        headers: {
          Authorization: `Bearer ${getAuthToken()}`
        }
      });

      setShowAddModal(false);

      // Add the new plan to the state
      if (response.data && response.data.plan) {
        setSubscriptionPlans(prev => [...prev, response.data.plan]);
      } else {
        // Fallback if the API doesn't return the created plan
        const newPlan: SubscriptionPlan = {
          id: Date.now().toString(),
          name: dataWithFeatures.name,
          description: dataWithFeatures.description,
          price_monthly: dataWithFeatures.price_monthly,
          price_yearly: dataWithFeatures.price_yearly,
          max_farms: dataWithFeatures.max_farms,
          max_users: dataWithFeatures.max_users,
          is_active: dataWithFeatures.is_active,
          is_trial: dataWithFeatures.is_trial,
          is_default: dataWithFeatures.is_default,
          trial_duration_days: dataWithFeatures.trial_duration_days,
          trial_features: dataWithFeatures.trial_features,
          requires_payment_method: dataWithFeatures.requires_payment_method,
          features: dataWithFeatures.features,
          created_at: new Date().toISOString()
        };
        setSubscriptionPlans(prev => [...prev, newPlan]);
      }
    } catch (error) {
      console.error('Error creating subscription plan:', error);
      alert('Failed to create subscription plan. Please try again.');
    }
  };

  const handleSubmitEdit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!currentPlan) return;

    const dataWithFeatures = prepareFormDataWithFeatures();

    try {
      // Make API call to update a plan
      const response = await axios.put(`${API_URL}/api/subscriptions/plans/${currentPlan.id}`, dataWithFeatures, {
        headers: {
          Authorization: `Bearer ${getAuthToken()}`
        }
      });

      setShowEditModal(false);

      // Update the plan in the state
      if (response.data && response.data.plan) {
        setSubscriptionPlans(prev => 
          prev.map(plan => 
            plan.id === currentPlan.id ? response.data.plan : plan
          )
        );
      } else {
        // Fallback if the API doesn't return the updated plan
        setSubscriptionPlans(prev => 
          prev.map(plan => 
            plan.id === currentPlan.id 
              ? { 
                  ...plan, 
                  name: dataWithFeatures.name,
                  description: dataWithFeatures.description,
                  price_monthly: dataWithFeatures.price_monthly,
                  price_yearly: dataWithFeatures.price_yearly,
                  max_farms: dataWithFeatures.max_farms,
                  max_users: dataWithFeatures.max_users,
                  is_active: dataWithFeatures.is_active,
                  features: dataWithFeatures.features
                } 
              : plan
          )
        );
      }
    } catch (error) {
      console.error('Error updating subscription plan:', error);
      alert('Failed to update subscription plan. Please try again.');
    }
  };

  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(price);
  };

  // Calculate days remaining in trial
  const getDaysRemaining = (endDate: string) => {
    const end = new Date(endDate);
    const now = new Date();
    const diffTime = end.getTime() - now.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return diffDays > 0 ? diffDays : 0;
  };

  if (loading) {
    return (
      <div className="p-8 text-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600 mx-auto"></div>
        <p className="mt-2 text-gray-500">Loading subscription plans...</p>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative" role="alert">
        <strong className="font-bold">Error!</strong>
        <span className="block sm:inline"> {error}</span>
      </div>
    );
  }

  return (
    <div>
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-xl font-semibold">Manage Subscription Plans</h2>
        <button 
          onClick={handleAddPlan}
          className="bg-primary-600 hover:bg-primary-700 text-white font-bold py-2 px-4 rounded"
        >
          Add Plan
        </button>
      </div>

      <div className="flex justify-center mb-8">
        <div className="relative bg-white rounded-lg p-1 flex">
          <button
            type="button"
            className={`${
              billingCycle === 'monthly'
                ? 'bg-primary-100 text-primary-800'
                : 'bg-white text-gray-500'
            } relative py-2 px-4 border-transparent rounded-md text-sm font-medium whitespace-nowrap focus:outline-none focus:ring-2 focus:ring-primary-500 focus:z-10`}
            onClick={() => setBillingCycle('monthly')}
          >
            Monthly billing
          </button>
          <button
            type="button"
            className={`${
              billingCycle === 'yearly'
                ? 'bg-primary-100 text-primary-800'
                : 'bg-white text-gray-500'
            } relative py-2 px-4 border-transparent rounded-md ml-0.5 text-sm font-medium whitespace-nowrap focus:outline-none focus:ring-2 focus:ring-primary-500 focus:z-10`}
            onClick={() => setBillingCycle('yearly')}
          >
            Yearly billing
            <span className="ml-1 text-xs text-green-500 font-semibold">Save 16%</span>
          </button>
        </div>
      </div>

      <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3">
        {subscriptionPlans.map(plan => (
          <div key={plan.id} className="bg-white shadow rounded-lg overflow-hidden hover:shadow-md transition-shadow duration-300">
            <div className="p-6">
              <div className="flex justify-between items-start">
                <h3 className="text-lg font-medium text-gray-900">{plan.name}</h3>
                <div className="flex flex-col items-end">
                  <span className={`px-2 py-1 text-xs font-semibold rounded-full ${
                    plan.is_active 
                      ? 'bg-green-100 text-green-800' 
                      : 'bg-red-100 text-red-800'
                  }`}>
                    {plan.is_active ? 'Active' : 'Inactive'}
                  </span>
                  {plan.is_trial && (
                    <span className="px-2 py-1 mt-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800">
                      Trial Plan
                    </span>
                  )}
                  {plan.is_default && (
                    <span className="px-2 py-1 mt-1 text-xs font-semibold rounded-full bg-purple-100 text-purple-800">
                      Default Trial
                    </span>
                  )}
                </div>
              </div>
              <div className="mb-4 mt-2">
                <span className="text-3xl font-bold text-gray-900">
                  {formatPrice(billingCycle === 'monthly' ? plan.price_monthly : plan.price_yearly)}
                </span>
                <span className="text-gray-500 ml-1">/{billingCycle === 'monthly' ? 'month' : 'year'}</span>
              </div>
              <p className="text-sm text-gray-500 mb-4">{plan.description}</p>
              {plan.is_trial && (
                <div className="mb-4 p-3 bg-blue-50 rounded-md">
                  <h4 className="text-sm font-medium text-blue-800 mb-1">Trial Information</h4>
                  <p className="text-xs text-blue-600">Duration: {plan.trial_duration_days} days</p>
                  <div className="mt-2 h-2 bg-blue-200 rounded-full overflow-hidden">
                    <div 
                      className="h-full bg-blue-600" 
                      style={{ width: `${Math.min(100, (plan.trial_duration_days - getDaysRemaining(plan.created_at)) / plan.trial_duration_days * 100)}%` }}
                    ></div>
                  </div>
                </div>
              )}
              <div className="mb-6">
                <h4 className="text-sm font-medium text-gray-900 mb-2">Features:</h4>
                <ul className="space-y-2">
                  {/* Group enabled features by category */}
                  {plan.features && Object.keys(plan.features).length > 0 ? (
                    <>
                      {/* Find the feature names from availableFeatures */}
                      {Array.from(new Set(
                        availableFeatures
                          .filter(feature => plan.features[feature.id])
                          .map(feature => feature.category)
                      )).map(category => (
                        <li key={category} className="flex items-start">
                          <svg className="h-5 w-5 text-green-500 mr-2" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                            <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                          </svg>
                          <div>
                            <span className="text-sm font-medium text-gray-700">{category}</span>
                            <ul className="ml-6 mt-1 space-y-1">
                              {availableFeatures
                                .filter(feature => feature.category === category && plan.features[feature.id])
                                .map(feature => (
                                  <li key={feature.id} className="text-xs text-gray-600">
                                    {feature.title}
                                  </li>
                                ))
                              }
                            </ul>
                          </div>
                        </li>
                      ))}
                    </>
                  ) : (
                    <li className="text-sm text-gray-600">No specific features enabled</li>
                  )}
                  <li className="flex items-start">
                    <svg className="h-5 w-5 text-green-500 mr-2" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                    </svg>
                    <span className="text-sm text-gray-600">Up to {plan.max_farms} farms</span>
                  </li>
                  <li className="flex items-start">
                    <svg className="h-5 w-5 text-green-500 mr-2" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                    </svg>
                    <span className="text-sm text-gray-600">Up to {plan.max_users} users</span>
                  </li>
                </ul>
              </div>
              <div className="mt-6 flex justify-between">
                <button 
                  onClick={() => handleEditPlan(plan)}
                  className="text-primary-600 hover:text-primary-900 font-medium"
                >
                  Edit Plan
                </button>
                <button
                  onClick={async () => {
                    if (confirm(`Are you sure you want to ${plan.is_active ? 'deactivate' : 'activate'} this plan?`)) {
                      try {
                        // Make API call to update the plan's active status
                        const response = await axios.put(
                          `${API_URL}/api/subscriptions/plans/${plan.id}`, 
                          { is_active: !plan.is_active },
                          {
                            headers: {
                              Authorization: `Bearer ${getAuthToken()}`
                            }
                          }
                        );

                        // Update the plan in the state
                        if (response.data && response.data.plan) {
                          setSubscriptionPlans(prev => 
                            prev.map(p => 
                              p.id === plan.id ? response.data.plan : p
                            )
                          );
                        } else {
                          // Fallback if the API doesn't return the updated plan
                          setSubscriptionPlans(prev => 
                            prev.map(p => 
                              p.id === plan.id 
                                ? { ...p, is_active: !p.is_active } 
                                : p
                            )
                          );
                        }
                      } catch (error) {
                        console.error(`Error ${plan.is_active ? 'deactivating' : 'activating'} subscription plan:`, error);
                        alert(`Failed to ${plan.is_active ? 'deactivate' : 'activate'} subscription plan. Please try again.`);
                      }
                    }
                  }}
                  className={`px-3 py-1 rounded text-sm font-medium ${
                    plan.is_active 
                      ? 'bg-red-100 text-red-800 hover:bg-red-200' 
                      : 'bg-green-100 text-green-800 hover:bg-green-200'
                  }`}
                >
                  {plan.is_active ? 'Deactivate' : 'Activate'}
                </button>
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Add Plan Modal */}
      {showAddModal && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full flex items-center justify-center">
          <div className="relative bg-white rounded-lg shadow-xl max-w-md w-full">
            <div className="flex justify-between items-center p-5 border-b">
              <h3 className="text-lg font-semibold">Add New Subscription Plan</h3>
              <button 
                onClick={() => setShowAddModal(false)}
                className="text-gray-400 hover:text-gray-500"
              >
                <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>
            <form onSubmit={handleSubmitAdd} className="p-5 max-h-[80vh] overflow-y-auto">
              <div className="mb-4">
                <label className="block text-gray-700 text-sm font-bold mb-2" htmlFor="name">
                  Plan Name
                </label>
                <input
                  type="text"
                  id="name"
                  name="name"
                  value={formData.name}
                  onChange={handleFormChange}
                  className="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                  required
                />
              </div>
              <div className="mb-4">
                <label className="block text-gray-700 text-sm font-bold mb-2" htmlFor="description">
                  Description
                </label>
                <textarea
                  id="description"
                  name="description"
                  value={formData.description}
                  onChange={handleFormChange}
                  className="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                  rows={3}
                />
              </div>
              <div className="mb-4 grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-gray-700 text-sm font-bold mb-2" htmlFor="price_monthly">
                    Monthly Price ($)
                  </label>
                  <input
                    type="number"
                    id="price_monthly"
                    name="price_monthly"
                    value={formData.price_monthly}
                    onChange={handleFormChange}
                    className="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                    min="0"
                    step="0.01"
                    required
                  />
                </div>
                <div>
                  <label className="block text-gray-700 text-sm font-bold mb-2" htmlFor="price_yearly">
                    Yearly Price ($)
                  </label>
                  <input
                    type="number"
                    id="price_yearly"
                    name="price_yearly"
                    value={formData.price_yearly}
                    onChange={handleFormChange}
                    className="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                    min="0"
                    step="0.01"
                    required
                  />
                </div>
              </div>
              <div className="mb-4 grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-gray-700 text-sm font-bold mb-2" htmlFor="max_farms">
                    Max Farms
                  </label>
                  <input
                    type="number"
                    id="max_farms"
                    name="max_farms"
                    value={formData.max_farms}
                    onChange={handleFormChange}
                    className="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                    min="0"
                    required
                  />
                </div>
                <div>
                  <label className="block text-gray-700 text-sm font-bold mb-2" htmlFor="max_users">
                    Max Users
                  </label>
                  <input
                    type="number"
                    id="max_users"
                    name="max_users"
                    value={formData.max_users}
                    onChange={handleFormChange}
                    className="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                    min="0"
                    required
                  />
                </div>
              </div>
              <div className="mb-4">
                <label className="flex items-center">
                  <input
                    type="checkbox"
                    name="is_active"
                    checked={formData.is_active}
                    onChange={handleFormChange}
                    className="form-checkbox h-5 w-5 text-primary-600"
                  />
                  <span className="ml-2 text-gray-700">Active</span>
                </label>
              </div>
              <div className="mb-4">
                <label className="flex items-center">
                  <input
                    type="checkbox"
                    name="is_trial"
                    checked={formData.is_trial}
                    onChange={handleFormChange}
                    className="form-checkbox h-5 w-5 text-primary-600"
                  />
                  <span className="ml-2 text-gray-700">Trial Plan</span>
                </label>
              </div>
              <div className="mb-4">
                <label className="flex items-center">
                  <input
                    type="checkbox"
                    name="is_default"
                    checked={formData.is_default}
                    onChange={handleFormChange}
                    className="form-checkbox h-5 w-5 text-primary-600"
                  />
                  <span className="ml-2 text-gray-700">Default Trial Plan</span>
                </label>
                <p className="text-xs text-gray-500 mt-1 ml-7">
                  Only one plan can be set as the default trial plan. This plan will be used for all new trial subscriptions.
                </p>
              </div>
              <div className="mb-4">
                <label className="block text-gray-700 text-sm font-bold mb-2" htmlFor="trial_duration_days">
                  Trial Duration (days)
                </label>
                <input
                  type="number"
                  id="trial_duration_days"
                  name="trial_duration_days"
                  value={formData.trial_duration_days}
                  onChange={handleFormChange}
                  className="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                  min="1"
                />
              </div>
              <div className="mb-4">
                <label className="block text-gray-700 text-sm font-bold mb-2">
                  Features
                </label>
                <p className="text-sm text-gray-500 mb-4">
                  Select which features will be available in this subscription plan. Users will only see menu items for features included in their plan. You can select/deselect entire categories by clicking on the category checkbox.
                </p>
                <div className="max-h-60 overflow-y-auto border rounded-md p-4">
                  {/* Group features by category */}
                  {Array.from(new Set(availableFeatures.map(f => f.category))).map(category => {
                    const featuresInCategory = availableFeatures.filter(feature => feature.category === category);
                    const allSelected = featuresInCategory.every(feature => !!selectedFeatures[feature.id]);
                    const someSelected = featuresInCategory.some(feature => !!selectedFeatures[feature.id]);

                    const handleCategoryToggle = () => {
                      const newSelectedFeatures = { ...selectedFeatures };
                      featuresInCategory.forEach(feature => {
                        newSelectedFeatures[feature.id] = !allSelected;
                      });
                      setSelectedFeatures(newSelectedFeatures);
                    };

                    return (
                      <div key={category} className="mb-4">
                        <div className="flex items-center mb-2">
                          <input
                            type="checkbox"
                            id={`category-${category}`}
                            checked={allSelected}
                            onChange={handleCategoryToggle}
                            className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
                            ref={el => {
                              if (el) {
                                el.indeterminate = someSelected && !allSelected;
                              }
                            }}
                          />
                          <label htmlFor={`category-${category}`} className="ml-2 font-medium text-gray-700">
                            {category}
                          </label>
                        </div>
                        <div className="space-y-2 ml-6">
                          {featuresInCategory.map(feature => (
                            <div key={feature.id} className="flex items-center">
                              <input
                                type="checkbox"
                                id={`feature-${feature.id}`}
                                checked={!!selectedFeatures[feature.id]}
                                onChange={() => handleFeatureToggle(feature.id)}
                                className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
                              />
                              <label htmlFor={`feature-${feature.id}`} className="ml-2 block text-sm text-gray-900">
                                {feature.title}
                              </label>
                            </div>
                          ))}
                        </div>
                      </div>
                    );
                  })}
                </div>
              </div>
              <div className="flex justify-end">
                <button
                  type="button"
                  onClick={() => setShowAddModal(false)}
                  className="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded mr-2"
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  className="bg-primary-600 hover:bg-primary-700 text-white font-bold py-2 px-4 rounded"
                >
                  Add Plan
                </button>
              </div>
            </form>
          </div>
        </div>
      )}

      {/* Edit Plan Modal */}
      {showEditModal && currentPlan && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full flex items-center justify-center">
          <div className="relative bg-white rounded-lg shadow-xl max-w-md w-full">
            <div className="flex justify-between items-center p-5 border-b">
              <h3 className="text-lg font-semibold">Edit Subscription Plan</h3>
              <button 
                onClick={() => setShowEditModal(false)}
                className="text-gray-400 hover:text-gray-500"
              >
                <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>
            <form onSubmit={handleSubmitEdit} className="p-5 max-h-[80vh] overflow-y-auto">
              <div className="mb-4">
                <label className="block text-gray-700 text-sm font-bold mb-2" htmlFor="edit-name">
                  Plan Name
                </label>
                <input
                  type="text"
                  id="edit-name"
                  name="name"
                  value={formData.name}
                  onChange={handleFormChange}
                  className="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                  required
                />
              </div>
              <div className="mb-4">
                <label className="block text-gray-700 text-sm font-bold mb-2" htmlFor="edit-description">
                  Description
                </label>
                <textarea
                  id="edit-description"
                  name="description"
                  value={formData.description}
                  onChange={handleFormChange}
                  className="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                  rows={3}
                />
              </div>
              <div className="mb-4 grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-gray-700 text-sm font-bold mb-2" htmlFor="edit-price_monthly">
                    Monthly Price ($)
                  </label>
                  <input
                    type="number"
                    id="edit-price_monthly"
                    name="price_monthly"
                    value={formData.price_monthly}
                    onChange={handleFormChange}
                    className="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                    min="0"
                    step="0.01"
                    required
                  />
                </div>
                <div>
                  <label className="block text-gray-700 text-sm font-bold mb-2" htmlFor="edit-price_yearly">
                    Yearly Price ($)
                  </label>
                  <input
                    type="number"
                    id="edit-price_yearly"
                    name="price_yearly"
                    value={formData.price_yearly}
                    onChange={handleFormChange}
                    className="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                    min="0"
                    step="0.01"
                    required
                  />
                </div>
              </div>
              <div className="mb-4 grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-gray-700 text-sm font-bold mb-2" htmlFor="edit-max_farms">
                    Max Farms
                  </label>
                  <input
                    type="number"
                    id="edit-max_farms"
                    name="max_farms"
                    value={formData.max_farms}
                    onChange={handleFormChange}
                    className="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                    min="0"
                    required
                  />
                </div>
                <div>
                  <label className="block text-gray-700 text-sm font-bold mb-2" htmlFor="edit-max_users">
                    Max Users
                  </label>
                  <input
                    type="number"
                    id="edit-max_users"
                    name="max_users"
                    value={formData.max_users}
                    onChange={handleFormChange}
                    className="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                    min="0"
                    required
                  />
                </div>
              </div>
              <div className="mb-4">
                <label className="flex items-center">
                  <input
                    type="checkbox"
                    name="is_active"
                    checked={formData.is_active}
                    onChange={handleFormChange}
                    className="form-checkbox h-5 w-5 text-primary-600"
                  />
                  <span className="ml-2 text-gray-700">Active</span>
                </label>
              </div>
              <div className="mb-4">
                <label className="flex items-center">
                  <input
                    type="checkbox"
                    name="is_trial"
                    checked={formData.is_trial}
                    onChange={handleFormChange}
                    className="form-checkbox h-5 w-5 text-primary-600"
                  />
                  <span className="ml-2 text-gray-700">Trial Plan</span>
                </label>
              </div>
              <div className="mb-4">
                <label className="flex items-center">
                  <input
                    type="checkbox"
                    name="is_default"
                    checked={formData.is_default}
                    onChange={handleFormChange}
                    className="form-checkbox h-5 w-5 text-primary-600"
                  />
                  <span className="ml-2 text-gray-700">Default Trial Plan</span>
                </label>
                <p className="text-xs text-gray-500 mt-1 ml-7">
                  Only one plan can be set as the default trial plan. This plan will be used for all new trial subscriptions.
                </p>
              </div>
              <div className="mb-4">
                <label className="block text-gray-700 text-sm font-bold mb-2" htmlFor="edit-trial_duration_days">
                  Trial Duration (days)
                </label>
                <input
                  type="number"
                  id="edit-trial_duration_days"
                  name="trial_duration_days"
                  value={formData.trial_duration_days}
                  onChange={handleFormChange}
                  className="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                  min="1"
                />
              </div>
              <div className="mb-4">
                <label className="block text-gray-700 text-sm font-bold mb-2">
                  Features
                </label>
                <p className="text-sm text-gray-500 mb-4">
                  Select which features will be available in this subscription plan. Users will only see menu items for features included in their plan. You can select/deselect entire categories by clicking on the category checkbox.
                </p>
                <div className="max-h-60 overflow-y-auto border rounded-md p-4">
                  {/* Group features by category */}
                  {Array.from(new Set(availableFeatures.map(f => f.category))).map(category => {
                    const featuresInCategory = availableFeatures.filter(feature => feature.category === category);
                    const allSelected = featuresInCategory.every(feature => !!selectedFeatures[feature.id]);
                    const someSelected = featuresInCategory.some(feature => !!selectedFeatures[feature.id]);

                    const handleCategoryToggle = () => {
                      const newSelectedFeatures = { ...selectedFeatures };
                      featuresInCategory.forEach(feature => {
                        newSelectedFeatures[feature.id] = !allSelected;
                      });
                      setSelectedFeatures(newSelectedFeatures);
                    };

                    return (
                      <div key={category} className="mb-4">
                        <div className="flex items-center mb-2">
                          <input
                            type="checkbox"
                            id={`edit-category-${category}`}
                            checked={allSelected}
                            onChange={handleCategoryToggle}
                            className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
                            ref={el => {
                              if (el) {
                                el.indeterminate = someSelected && !allSelected;
                              }
                            }}
                          />
                          <label htmlFor={`edit-category-${category}`} className="ml-2 font-medium text-gray-700">
                            {category}
                          </label>
                        </div>
                        <div className="space-y-2 ml-6">
                          {featuresInCategory.map(feature => (
                            <div key={feature.id} className="flex items-center">
                              <input
                                type="checkbox"
                                id={`edit-feature-${feature.id}`}
                                checked={!!selectedFeatures[feature.id]}
                                onChange={() => handleFeatureToggle(feature.id)}
                                className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
                              />
                              <label htmlFor={`edit-feature-${feature.id}`} className="ml-2 block text-sm text-gray-900">
                                {feature.title}
                              </label>
                            </div>
                          ))}
                        </div>
                      </div>
                    );
                  })}
                </div>
              </div>
              <div className="flex justify-end">
                <button
                  type="button"
                  onClick={() => setShowEditModal(false)}
                  className="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded mr-2"
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  className="bg-primary-600 hover:bg-primary-700 text-white font-bold py-2 px-4 rounded"
                >
                  Update Plan
                </button>
              </div>
            </form>
          </div>
        </div>
      )}
    </div>
  );
};

export default SubscriptionManagement;
