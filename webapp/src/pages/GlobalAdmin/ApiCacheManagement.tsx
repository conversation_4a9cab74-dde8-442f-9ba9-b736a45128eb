import React, { useState, useEffect } from 'react';
import axios from 'axios';
import { API_URL } from '../../config';
import { getAuthToken } from '../utils/storageUtils';

interface ApiProvider {
  id: string;
  name: string;
  category: string;
}

interface ApiEndpoint {
  id: string;
  name: string;
  path: string;
  method: string;
  ApiProvider?: ApiProvider;
}

interface ApiCache {
  id: string;
  endpoint_id: string;
  cache_key: string;
  request_params: any;
  response_body: any;
  created_at: string;
  expires_at: string;
  last_accessed_at: string;
  ApiEndpoint?: ApiEndpoint;
}

interface PaginationData {
  total: number;
  page: number;
  limit: number;
  pages: number;
}

const ApiCacheManagement: React.FC = () => {
  const [providers, setProviders] = useState<ApiProvider[]>([]);
  const [endpoints, setEndpoints] = useState<ApiEndpoint[]>([]);
  const [cacheData, setCacheData] = useState<ApiCache[]>([]);
  const [pagination, setPagination] = useState<PaginationData>({
    total: 0,
    page: 1,
    limit: 20,
    pages: 0
  });
  const [selectedProvider, setSelectedProvider] = useState<string>('');
  const [selectedEndpoint, setSelectedEndpoint] = useState<string>('');
  const [searchTerm, setSearchTerm] = useState<string>('');
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [viewingCache, setViewingCache] = useState<ApiCache | null>(null);
  const [cleanupStatus, setCleanupStatus] = useState<string | null>(null);

  useEffect(() => {
    const fetchInitialData = async () => {
      try {
        setLoading(true);
        setError(null);

        // Fetch API providers
        const providersResponse = await axios.get(`${API_URL}/api/admin/api-providers`, {
          headers: {
            Authorization: `Bearer ${getAuthToken()}`
          }
        });
        setProviders(providersResponse.data || []);

        // Fetch initial cache data
        await fetchCacheData(1);

        setLoading(false);
      } catch (err: any) {
        console.error('Error fetching initial data:', err);
        setError(err.response?.data?.error || 'Failed to load initial data');
        setLoading(false);
      }
    };

    fetchInitialData();
  }, []);

  const fetchCacheData = async (page: number = 1) => {
    try {
      setLoading(true);
      setError(null);

      // Build query parameters
      const params: any = { page, limit: pagination.limit };
      if (selectedProvider) params.providerId = selectedProvider;
      if (selectedEndpoint) params.endpointId = selectedEndpoint;
      if (searchTerm) params.search = searchTerm;

      // Fetch cache data
      const response = await axios.get(`${API_URL}/api/admin/api-cache`, {
        params,
        headers: {
          Authorization: `Bearer ${getAuthToken()}`
        }
      });

      setCacheData(response.data.data || []);
      setPagination(response.data.pagination);
      setLoading(false);
    } catch (err: any) {
      console.error('Error fetching cache data:', err);
      setError(err.response?.data?.error || 'Failed to load cache data');
      setLoading(false);
    }
  };

  const handleProviderChange = async (providerId: string) => {
    setSelectedProvider(providerId);
    setSelectedEndpoint(''); // Reset endpoint selection when provider changes

    if (providerId) {
      try {
        // Fetch endpoints for the selected provider
        const response = await axios.get(`${API_URL}/api/admin/api-endpoints?providerId=${providerId}`, {
          headers: {
            Authorization: `Bearer ${getAuthToken()}`
          }
        });
        setEndpoints(response.data || []);
      } catch (err: any) {
        console.error('Error fetching endpoints:', err);
        setError(err.response?.data?.error || 'Failed to load endpoints');
      }
    } else {
      setEndpoints([]);
    }

    // Fetch cache data with the new filter
    await fetchCacheData(1);
  };

  const handleEndpointChange = async (endpointId: string) => {
    setSelectedEndpoint(endpointId);
    // Fetch cache data with the new filter
    await fetchCacheData(1);
  };

  const handleSearch = async (e: React.FormEvent) => {
    e.preventDefault();
    await fetchCacheData(1);
  };

  const handlePageChange = async (newPage: number) => {
    if (newPage < 1 || newPage > pagination.pages) return;
    await fetchCacheData(newPage);
  };

  const handleViewCache = (cache: ApiCache) => {
    setViewingCache(cache);
  };

  const handleCloseView = () => {
    setViewingCache(null);
  };

  const handleDeleteCache = async (cacheId: string) => {
    if (!window.confirm('Are you sure you want to delete this cached item?')) return;

    try {
      setLoading(true);
      await axios.delete(`${API_URL}/api/admin/api-cache/${cacheId}`, {
        headers: {
          Authorization: `Bearer ${getAuthToken()}`
        }
      });

      // Refresh cache data
      await fetchCacheData(pagination.page);
      
      if (viewingCache?.id === cacheId) {
        setViewingCache(null);
      }
    } catch (err: any) {
      console.error('Error deleting cache:', err);
      setError(err.response?.data?.error || 'Failed to delete cache');
      setLoading(false);
    }
  };

  const handleCleanupCache = async () => {
    if (!window.confirm('Are you sure you want to clean up expired cache entries?')) return;

    try {
      setLoading(true);
      setCleanupStatus(null);

      const response = await axios.post(`${API_URL}/api/admin/api-cache/cleanup`, {}, {
        headers: {
          Authorization: `Bearer ${getAuthToken()}`
        }
      });

      setCleanupStatus(response.data.message);
      
      // Refresh cache data
      await fetchCacheData(pagination.page);
    } catch (err: any) {
      console.error('Error cleaning up cache:', err);
      setError(err.response?.data?.error || 'Failed to clean up cache');
      setLoading(false);
    }
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleString();
  };

  const formatExpiryStatus = (expiryDate: string) => {
    const now = new Date();
    const expiry = new Date(expiryDate);
    
    if (expiry < now) {
      return <span className="text-red-600">Expired</span>;
    }
    
    const diffMs = expiry.getTime() - now.getTime();
    const diffHrs = Math.floor(diffMs / (1000 * 60 * 60));
    
    if (diffHrs < 1) {
      return <span className="text-orange-600">Expires soon</span>;
    }
    
    if (diffHrs < 24) {
      return <span className="text-yellow-600">Expires in {diffHrs}h</span>;
    }
    
    return <span className="text-green-600">Valid</span>;
  };

  if (loading && providers.length === 0) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary-500"></div>
      </div>
    );
  }

  if (error && providers.length === 0) {
    return (
      <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative" role="alert">
        <strong className="font-bold">Error!</strong>
        <span className="block sm:inline"> {error}</span>
      </div>
    );
  }

  return (
    <div>
      <div className="mb-6">
        <h2 className="text-xl font-semibold mb-4">API Cache Management</h2>
        <p className="text-gray-600 mb-4">
          View, search, and manage cached API data. You can filter by provider and endpoint, search for specific data, and delete cached items.
        </p>

        {/* Cleanup status message */}
        {cleanupStatus && (
          <div className="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded relative mb-4" role="alert">
            <span className="block sm:inline">{cleanupStatus}</span>
            <button 
              className="absolute top-0 bottom-0 right-0 px-4 py-3"
              onClick={() => setCleanupStatus(null)}
            >
              <span className="sr-only">Close</span>
              <svg className="h-6 w-6 text-green-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>
        )}
        
        {/* Filter and search form */}
        <div className="bg-white p-6 rounded-lg shadow-md mb-6">
          <form onSubmit={handleSearch} className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <label htmlFor="provider-filter" className="block text-sm font-medium text-gray-700 mb-1">
                  Filter by Provider:
                </label>
                <select
                  id="provider-filter"
                  className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500"
                  value={selectedProvider}
                  onChange={(e) => handleProviderChange(e.target.value)}
                >
                  <option value="">All Providers</option>
                  {providers.map(provider => (
                    <option key={provider.id} value={provider.id}>
                      {provider.name} ({provider.category})
                    </option>
                  ))}
                </select>
              </div>
              
              <div>
                <label htmlFor="endpoint-filter" className="block text-sm font-medium text-gray-700 mb-1">
                  Filter by Endpoint:
                </label>
                <select
                  id="endpoint-filter"
                  className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500"
                  value={selectedEndpoint}
                  onChange={(e) => handleEndpointChange(e.target.value)}
                  disabled={!selectedProvider || endpoints.length === 0}
                >
                  <option value="">All Endpoints</option>
                  {endpoints.map(endpoint => (
                    <option key={endpoint.id} value={endpoint.id}>
                      {endpoint.name} ({endpoint.method})
                    </option>
                  ))}
                </select>
              </div>
              
              <div>
                <label htmlFor="search" className="block text-sm font-medium text-gray-700 mb-1">
                  Search:
                </label>
                <div className="flex">
                  <input
                    type="text"
                    id="search"
                    className="flex-1 px-3 py-2 border border-gray-300 rounded-l-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500"
                    placeholder="Search in cache data..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                  />
                  <button
                    type="submit"
                    className="px-4 py-2 border border-transparent rounded-r-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
                  >
                    Search
                  </button>
                </div>
              </div>
            </div>
            
            <div className="flex justify-end">
              <button
                type="button"
                onClick={handleCleanupCache}
                className="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
              >
                Clean Up Expired Cache
              </button>
            </div>
          </form>
        </div>

        {/* Cache data view modal */}
        {viewingCache && (
          <div className="fixed inset-0 z-50 overflow-y-auto">
            <div className="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
              <div className="fixed inset-0 transition-opacity" aria-hidden="true">
                <div className="absolute inset-0 bg-gray-500 opacity-75"></div>
              </div>

              <span className="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>

              <div className="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-4xl sm:w-full">
                <div className="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                  <div className="sm:flex sm:items-start">
                    <div className="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left w-full">
                      <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4">
                        Cache Details
                      </h3>
                      
                      <div className="mb-4">
                        <h4 className="text-sm font-medium text-gray-700 mb-1">Provider:</h4>
                        <p className="text-sm text-gray-900">
                          {viewingCache.ApiEndpoint?.ApiProvider?.name || 'Unknown Provider'}
                        </p>
                      </div>
                      
                      <div className="mb-4">
                        <h4 className="text-sm font-medium text-gray-700 mb-1">Endpoint:</h4>
                        <p className="text-sm text-gray-900">
                          {viewingCache.ApiEndpoint?.name || 'Unknown Endpoint'} ({viewingCache.ApiEndpoint?.method || 'Unknown Method'})
                        </p>
                      </div>
                      
                      <div className="mb-4">
                        <h4 className="text-sm font-medium text-gray-700 mb-1">Cache Key:</h4>
                        <p className="text-sm text-gray-900 font-mono bg-gray-100 p-2 rounded">
                          {viewingCache.cache_key}
                        </p>
                      </div>
                      
                      <div className="mb-4">
                        <h4 className="text-sm font-medium text-gray-700 mb-1">Request Parameters:</h4>
                        <pre className="text-sm text-gray-900 font-mono bg-gray-100 p-2 rounded overflow-auto max-h-40">
                          {JSON.stringify(viewingCache.request_params, null, 2)}
                        </pre>
                      </div>
                      
                      <div className="mb-4">
                        <h4 className="text-sm font-medium text-gray-700 mb-1">Response Body:</h4>
                        <pre className="text-sm text-gray-900 font-mono bg-gray-100 p-2 rounded overflow-auto max-h-60">
                          {JSON.stringify(viewingCache.response_body, null, 2)}
                        </pre>
                      </div>
                      
                      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                        <div>
                          <h4 className="text-sm font-medium text-gray-700 mb-1">Created At:</h4>
                          <p className="text-sm text-gray-900">
                            {formatDate(viewingCache.created_at)}
                          </p>
                        </div>
                        
                        <div>
                          <h4 className="text-sm font-medium text-gray-700 mb-1">Expires At:</h4>
                          <p className="text-sm text-gray-900">
                            {formatDate(viewingCache.expires_at)} ({formatExpiryStatus(viewingCache.expires_at)})
                          </p>
                        </div>
                        
                        <div>
                          <h4 className="text-sm font-medium text-gray-700 mb-1">Last Accessed:</h4>
                          <p className="text-sm text-gray-900">
                            {formatDate(viewingCache.last_accessed_at)}
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <div className="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
                  <button
                    type="button"
                    onClick={() => handleDeleteCache(viewingCache.id)}
                    className="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-red-600 text-base font-medium text-white hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 sm:ml-3 sm:w-auto sm:text-sm"
                  >
                    Delete Cache
                  </button>
                  <button
                    type="button"
                    onClick={handleCloseView}
                    className="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm"
                  >
                    Close
                  </button>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Cache data table */}
        {loading && cacheData.length === 0 ? (
          <div className="flex justify-center items-center h-32">
            <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-primary-500"></div>
          </div>
        ) : cacheData.length === 0 ? (
          <div className="bg-white p-6 rounded-lg shadow-md text-center">
            <p className="text-gray-500">No cached data found with the current filters.</p>
          </div>
        ) : (
          <>
            <div className="bg-white shadow-md rounded-lg overflow-hidden">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Provider / Endpoint
                    </th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Cache Key
                    </th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Created At
                    </th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Expires At
                    </th>
                    <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Actions
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {cacheData.map(cache => (
                    <tr key={cache.id}>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm font-medium text-gray-900">
                          {cache.ApiEndpoint?.ApiProvider?.name || 'Unknown Provider'}
                        </div>
                        <div className="text-xs text-gray-500">
                          {cache.ApiEndpoint?.name || 'Unknown Endpoint'} ({cache.ApiEndpoint?.method || 'Unknown Method'})
                        </div>
                      </td>
                      <td className="px-6 py-4">
                        <div className="text-sm text-gray-900 font-mono truncate max-w-xs">
                          {cache.cache_key}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-900">
                          {formatDate(cache.created_at)}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-900">
                          {formatDate(cache.expires_at)}
                        </div>
                        <div className="text-xs">
                          {formatExpiryStatus(cache.expires_at)}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                        <button
                          onClick={() => handleViewCache(cache)}
                          className="text-primary-600 hover:text-primary-900 mr-4"
                        >
                          View
                        </button>
                        <button
                          onClick={() => handleDeleteCache(cache.id)}
                          className="text-red-600 hover:text-red-900"
                        >
                          Delete
                        </button>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>

            {/* Pagination */}
            {pagination.pages > 1 && (
              <div className="flex justify-center mt-6">
                <nav className="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                  <button
                    onClick={() => handlePageChange(pagination.page - 1)}
                    disabled={pagination.page === 1}
                    className={`relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium ${
                      pagination.page === 1 
                        ? 'text-gray-300 cursor-not-allowed' 
                        : 'text-gray-500 hover:bg-gray-50'
                    }`}
                  >
                    <span className="sr-only">Previous</span>
                    <svg className="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                      <path fillRule="evenodd" d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z" clipRule="evenodd" />
                    </svg>
                  </button>
                  
                  {/* Page numbers */}
                  {Array.from({ length: Math.min(5, pagination.pages) }, (_, i) => {
                    // Show pages around the current page
                    let pageNum;
                    if (pagination.pages <= 5) {
                      pageNum = i + 1;
                    } else if (pagination.page <= 3) {
                      pageNum = i + 1;
                    } else if (pagination.page >= pagination.pages - 2) {
                      pageNum = pagination.pages - 4 + i;
                    } else {
                      pageNum = pagination.page - 2 + i;
                    }
                    
                    return (
                      <button
                        key={pageNum}
                        onClick={() => handlePageChange(pageNum)}
                        className={`relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium ${
                          pagination.page === pageNum
                            ? 'z-10 bg-primary-50 border-primary-500 text-primary-600'
                            : 'text-gray-500 hover:bg-gray-50'
                        }`}
                      >
                        {pageNum}
                      </button>
                    );
                  })}
                  
                  <button
                    onClick={() => handlePageChange(pagination.page + 1)}
                    disabled={pagination.page === pagination.pages}
                    className={`relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium ${
                      pagination.page === pagination.pages 
                        ? 'text-gray-300 cursor-not-allowed' 
                        : 'text-gray-500 hover:bg-gray-50'
                    }`}
                  >
                    <span className="sr-only">Next</span>
                    <svg className="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                      <path fillRule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clipRule="evenodd" />
                    </svg>
                  </button>
                </nav>
              </div>
            )}
          </>
        )}
      </div>
    </div>
  );
};

export default ApiCacheManagement;