import React, { useState, useEffect } from 'react';
import axios from 'axios';
import { API_URL } from '../../config';
import { format } from 'date-fns';

interface PromoCode {
  id: string;
  code: string;
  description: string;
  subscription_plan_id: string | null;
  valid_from: string;
  valid_to: string | null;
  max_uses: number | null;
  current_uses: number;
  user_id: string | null;
  farm_id: string | null;
  discount_percent: number | null;
  discount_amount: number | null;
  discount_months: number | null;
  applies_to_monthly: boolean;
  applies_to_yearly: boolean;
  is_active: boolean;
  created_at: string;
  updated_at: string;
  subscriptionPlan?: {
    id: string;
    name: string;
  };
  user?: {
    id: string;
    email: string;
    first_name: string;
    last_name: string;
  };
  farm?: {
    id: string;
    name: string;
  };
}

interface SubscriptionPlan {
  id: string;
  name: string;
}

interface User {
  id: string;
  email: string;
  first_name: string;
  last_name: string;
}

interface Farm {
  id: string;
  name: string;
}

const PromoCodeManagement: React.FC = () => {
  const [promoCodes, setPromoCodes] = useState<PromoCode[]>([]);
  const [subscriptionPlans, setSubscriptionPlans] = useState<SubscriptionPlan[]>([]);
  const [users, setUsers] = useState<User[]>([]);
  const [farms, setFarms] = useState<Farm[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [showAddModal, setShowAddModal] = useState<boolean>(false);
  const [showEditModal, setShowEditModal] = useState<boolean>(false);
  const [currentPromoCode, setCurrentPromoCode] = useState<PromoCode | null>(null);
  const [searchTerm, setSearchTerm] = useState<string>('');
  const [userSearchTerm, setUserSearchTerm] = useState<string>('');
  const [farmSearchTerm, setFarmSearchTerm] = useState<string>('');
  const [filteredUsers, setFilteredUsers] = useState<User[]>([]);
  const [filteredFarms, setFilteredFarms] = useState<Farm[]>([]);
  const [formData, setFormData] = useState({
    code: '',
    description: '',
    subscription_plan_id: '',
    valid_from: new Date().toISOString().split('T')[0],
    valid_to: '',
    max_uses: '',
    user_id: '',
    farm_id: '',
    discount_percent: '',
    discount_amount: '',
    discount_months: '',
    applies_to_monthly: true,
    applies_to_yearly: true,
    is_active: true
  });
  const [discountType, setDiscountType] = useState<'percent' | 'amount'>('percent');

  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);

        // Fetch promo codes
        const promoCodesResponse = await axios.get(`${API_URL}/api/subscriptions/promo-codes`, {
          headers: {
            Authorization: `Bearer ${localStorage.getItem('token')}`
          }
        });

        setPromoCodes(promoCodesResponse.data.promoCodes || []);

        // Fetch subscription plans
        const plansResponse = await axios.get(`${API_URL}/api/subscriptions/plans`, {
          headers: {
            Authorization: `Bearer ${localStorage.getItem('token')}`
          }
        });

        setSubscriptionPlans(plansResponse.data.plans || []);

        // Fetch users (for autocomplete)
        const usersResponse = await axios.get(`${API_URL}/api/users`, {
          headers: {
            Authorization: `Bearer ${localStorage.getItem('token')}`
          }
        });

        setUsers(usersResponse.data.users || []);

        // Fetch farms (for autocomplete)
        const farmsResponse = await axios.get(`${API_URL}/api/farms`, {
          headers: {
            Authorization: `Bearer ${localStorage.getItem('token')}`
          }
        });

        setFarms(farmsResponse.data.farms || []);

        setLoading(false);
      } catch (err) {
        console.error('Error fetching data:', err);
        setError('Failed to load data');
        setLoading(false);
      }
    };

    fetchData();
  }, []);

  useEffect(() => {
    // Filter users based on search term
    if (userSearchTerm) {
      const filtered = users.filter(user => 
        user.email.toLowerCase().includes(userSearchTerm.toLowerCase()) ||
        `${user.first_name} ${user.last_name}`.toLowerCase().includes(userSearchTerm.toLowerCase())
      );
      setFilteredUsers(filtered);
    } else {
      setFilteredUsers([]);
    }
  }, [userSearchTerm, users]);

  useEffect(() => {
    // Filter farms based on search term
    if (farmSearchTerm) {
      const filtered = farms.filter(farm => 
        farm.name.toLowerCase().includes(farmSearchTerm.toLowerCase())
      );
      setFilteredFarms(filtered);
    } else {
      setFilteredFarms([]);
    }
  }, [farmSearchTerm, farms]);

  const handleAddPromoCode = () => {
    setFormData({
      code: '',
      description: '',
      subscription_plan_id: '',
      valid_from: new Date().toISOString().split('T')[0],
      valid_to: '',
      max_uses: '',
      user_id: '',
      farm_id: '',
      discount_percent: '',
      discount_amount: '',
      discount_months: '',
      applies_to_monthly: true,
      applies_to_yearly: true,
      is_active: true
    });
    setDiscountType('percent');
    setShowAddModal(true);
  };

  const handleEditPromoCode = (promoCode: PromoCode) => {
    setCurrentPromoCode(promoCode);
    setFormData({
      code: promoCode.code,
      description: promoCode.description || '',
      subscription_plan_id: promoCode.subscription_plan_id || '',
      valid_from: promoCode.valid_from ? new Date(promoCode.valid_from).toISOString().split('T')[0] : new Date().toISOString().split('T')[0],
      valid_to: promoCode.valid_to ? new Date(promoCode.valid_to).toISOString().split('T')[0] : '',
      max_uses: promoCode.max_uses !== null ? promoCode.max_uses.toString() : '',
      user_id: promoCode.user_id || '',
      farm_id: promoCode.farm_id || '',
      discount_percent: promoCode.discount_percent !== null ? promoCode.discount_percent.toString() : '',
      discount_amount: promoCode.discount_amount !== null ? promoCode.discount_amount.toString() : '',
      discount_months: promoCode.discount_months !== null ? promoCode.discount_months.toString() : '',
      applies_to_monthly: promoCode.applies_to_monthly,
      applies_to_yearly: promoCode.applies_to_yearly,
      is_active: promoCode.is_active
    });
    setDiscountType(promoCode.discount_percent !== null ? 'percent' : 'amount');
    setShowEditModal(true);
  };

  const handleFormChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value, type } = e.target as HTMLInputElement;

    if (type === 'checkbox') {
      const { checked } = e.target as HTMLInputElement;
      setFormData(prev => ({ ...prev, [name]: checked }));
    } else if (type === 'number') {
      setFormData(prev => ({ ...prev, [name]: value }));
    } else {
      setFormData(prev => ({ ...prev, [name]: value }));
    }
  };

  const handleDiscountTypeChange = (type: 'percent' | 'amount') => {
    setDiscountType(type);
    // Clear the other discount type
    if (type === 'percent') {
      setFormData(prev => ({ ...prev, discount_amount: '' }));
    } else {
      setFormData(prev => ({ ...prev, discount_percent: '' }));
    }
  };

  const handleUserSelect = (user: User) => {
    setFormData(prev => ({ ...prev, user_id: user.id }));
    setUserSearchTerm(`${user.first_name} ${user.last_name} (${user.email})`);
    setFilteredUsers([]);
  };

  const handleFarmSelect = (farm: Farm) => {
    setFormData(prev => ({ ...prev, farm_id: farm.id }));
    setFarmSearchTerm(farm.name);
    setFilteredFarms([]);
  };

  const handleSubmitAdd = async (e: React.FormEvent) => {
    e.preventDefault();
    
    try {
      const payload = {
        code: formData.code,
        description: formData.description || null,
        subscription_plan_id: formData.subscription_plan_id || null,
        valid_from: formData.valid_from ? new Date(formData.valid_from) : new Date(),
        valid_to: formData.valid_to ? new Date(formData.valid_to) : null,
        max_uses: formData.max_uses ? parseInt(formData.max_uses) : null,
        user_id: formData.user_id || null,
        farm_id: formData.farm_id || null,
        discount_percent: discountType === 'percent' && formData.discount_percent ? parseFloat(formData.discount_percent) : null,
        discount_amount: discountType === 'amount' && formData.discount_amount ? parseFloat(formData.discount_amount) : null,
        discount_months: formData.discount_months ? parseInt(formData.discount_months) : null,
        applies_to_monthly: formData.applies_to_monthly,
        applies_to_yearly: formData.applies_to_yearly,
        is_active: formData.is_active
      };

      const response = await axios.post(`${API_URL}/api/subscriptions/promo-codes`, payload, {
        headers: {
          Authorization: `Bearer ${localStorage.getItem('token')}`
        }
      });

      setShowAddModal(false);

      // Add the new promo code to the state
      if (response.data && response.data.promoCode) {
        setPromoCodes(prev => [response.data.promoCode, ...prev]);
      }
    } catch (error) {
      console.error('Error creating promo code:', error);
      alert('Failed to create promo code. Please try again.');
    }
  };

  const handleSubmitEdit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!currentPromoCode) return;

    try {
      const payload = {
        code: formData.code,
        description: formData.description || null,
        subscription_plan_id: formData.subscription_plan_id || null,
        valid_from: formData.valid_from ? new Date(formData.valid_from) : new Date(),
        valid_to: formData.valid_to ? new Date(formData.valid_to) : null,
        max_uses: formData.max_uses ? parseInt(formData.max_uses) : null,
        user_id: formData.user_id || null,
        farm_id: formData.farm_id || null,
        discount_percent: discountType === 'percent' && formData.discount_percent ? parseFloat(formData.discount_percent) : null,
        discount_amount: discountType === 'amount' && formData.discount_amount ? parseFloat(formData.discount_amount) : null,
        discount_months: formData.discount_months ? parseInt(formData.discount_months) : null,
        applies_to_monthly: formData.applies_to_monthly,
        applies_to_yearly: formData.applies_to_yearly,
        is_active: formData.is_active
      };

      const response = await axios.put(`${API_URL}/api/subscriptions/promo-codes/${currentPromoCode.id}`, payload, {
        headers: {
          Authorization: `Bearer ${localStorage.getItem('token')}`
        }
      });

      setShowEditModal(false);

      // Update the promo code in the state
      if (response.data && response.data.promoCode) {
        setPromoCodes(prev => 
          prev.map(code => 
            code.id === currentPromoCode.id ? response.data.promoCode : code
          )
        );
      }
    } catch (error) {
      console.error('Error updating promo code:', error);
      alert('Failed to update promo code. Please try again.');
    }
  };

  const handleDeletePromoCode = async (promoCodeId: string) => {
    if (confirm('Are you sure you want to delete this promo code? This action cannot be undone.')) {
      try {
        await axios.delete(`${API_URL}/api/subscriptions/promo-codes/${promoCodeId}`, {
          headers: {
            Authorization: `Bearer ${localStorage.getItem('token')}`
          }
        });

        // Remove the promo code from the state
        setPromoCodes(prev => prev.filter(code => code.id !== promoCodeId));
      } catch (error) {
        console.error('Error deleting promo code:', error);
        alert('Failed to delete promo code. Please try again.');
      }
    }
  };

  const handleToggleActive = async (promoCode: PromoCode) => {
    try {
      const response = await axios.put(
        `${API_URL}/api/subscriptions/promo-codes/${promoCode.id}`,
        { is_active: !promoCode.is_active },
        {
          headers: {
            Authorization: `Bearer ${localStorage.getItem('token')}`
          }
        }
      );

      // Update the promo code in the state
      if (response.data && response.data.promoCode) {
        setPromoCodes(prev => 
          prev.map(code => 
            code.id === promoCode.id ? response.data.promoCode : code
          )
        );
      }
    } catch (error) {
      console.error('Error toggling promo code active status:', error);
      alert('Failed to update promo code. Please try again.');
    }
  };

  const filteredPromoCodes = promoCodes.filter(code => 
    code.code.toLowerCase().includes(searchTerm.toLowerCase()) ||
    (code.description && code.description.toLowerCase().includes(searchTerm.toLowerCase()))
  );

  const formatDate = (dateString: string | null) => {
    if (!dateString) return 'No expiration';
    return format(new Date(dateString), 'MMM d, yyyy');
  };

  if (loading) {
    return (
      <div className="p-8 text-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600 mx-auto"></div>
        <p className="mt-2 text-gray-500">Loading promo codes...</p>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative" role="alert">
        <strong className="font-bold">Error!</strong>
        <span className="block sm:inline"> {error}</span>
      </div>
    );
  }

  return (
    <div>
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-xl font-semibold">Manage Promo Codes</h2>
        <button 
          onClick={handleAddPromoCode}
          className="bg-primary-600 hover:bg-primary-700 text-white font-bold py-2 px-4 rounded"
        >
          Add Promo Code
        </button>
      </div>

      <div className="mb-4">
        <input
          type="text"
          placeholder="Search promo codes..."
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          className="w-full p-2 border border-gray-300 rounded"
        />
      </div>

      <div className="overflow-x-auto">
        <table className="min-w-full bg-white">
          <thead className="bg-gray-100">
            <tr>
              <th className="py-2 px-4 border-b text-left">Code</th>
              <th className="py-2 px-4 border-b text-left">Description</th>
              <th className="py-2 px-4 border-b text-left">Discount</th>
              <th className="py-2 px-4 border-b text-left">Valid Period</th>
              <th className="py-2 px-4 border-b text-left">Usage</th>
              <th className="py-2 px-4 border-b text-left">Status</th>
              <th className="py-2 px-4 border-b text-left">Actions</th>
            </tr>
          </thead>
          <tbody>
            {filteredPromoCodes.length > 0 ? (
              filteredPromoCodes.map(promoCode => (
                <tr key={promoCode.id} className="hover:bg-gray-50">
                  <td className="py-2 px-4 border-b">{promoCode.code}</td>
                  <td className="py-2 px-4 border-b">{promoCode.description || '-'}</td>
                  <td className="py-2 px-4 border-b">
                    {promoCode.discount_percent ? `${promoCode.discount_percent}%` : ''}
                    {promoCode.discount_amount ? `$${promoCode.discount_amount}` : ''}
                    {promoCode.discount_months ? ` for ${promoCode.discount_months} month${promoCode.discount_months > 1 ? 's' : ''}` : ''}
                  </td>
                  <td className="py-2 px-4 border-b">
                    {formatDate(promoCode.valid_from)} - {formatDate(promoCode.valid_to)}
                  </td>
                  <td className="py-2 px-4 border-b">
                    {promoCode.current_uses} / {promoCode.max_uses || '∞'}
                  </td>
                  <td className="py-2 px-4 border-b">
                    <span className={`px-2 py-1 text-xs font-semibold rounded-full ${
                      promoCode.is_active 
                        ? 'bg-green-100 text-green-800' 
                        : 'bg-red-100 text-red-800'
                    }`}>
                      {promoCode.is_active ? 'Active' : 'Inactive'}
                    </span>
                  </td>
                  <td className="py-2 px-4 border-b">
                    <div className="flex space-x-2">
                      <button 
                        onClick={() => handleEditPromoCode(promoCode)}
                        className="text-blue-600 hover:text-blue-800"
                      >
                        Edit
                      </button>
                      <button 
                        onClick={() => handleToggleActive(promoCode)}
                        className={`${
                          promoCode.is_active 
                            ? 'text-red-600 hover:text-red-800' 
                            : 'text-green-600 hover:text-green-800'
                        }`}
                      >
                        {promoCode.is_active ? 'Deactivate' : 'Activate'}
                      </button>
                      <button 
                        onClick={() => handleDeletePromoCode(promoCode.id)}
                        className="text-red-600 hover:text-red-800"
                      >
                        Delete
                      </button>
                    </div>
                  </td>
                </tr>
              ))
            ) : (
              <tr>
                <td colSpan={7} className="py-4 text-center text-gray-500">
                  No promo codes found
                </td>
              </tr>
            )}
          </tbody>
        </table>
      </div>

      {/* Add Promo Code Modal */}
      {showAddModal && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full flex items-center justify-center">
          <div className="relative bg-white rounded-lg shadow-xl max-w-md w-full">
            <div className="flex justify-between items-center p-5 border-b">
              <h3 className="text-lg font-semibold">Add New Promo Code</h3>
              <button 
                onClick={() => setShowAddModal(false)}
                className="text-gray-400 hover:text-gray-500"
              >
                <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>
            <form onSubmit={handleSubmitAdd} className="p-5 max-h-[80vh] overflow-y-auto">
              {/* Form fields will go here */}
              <div className="flex justify-end">
                <button
                  type="button"
                  onClick={() => setShowAddModal(false)}
                  className="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded mr-2"
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  className="bg-primary-600 hover:bg-primary-700 text-white font-bold py-2 px-4 rounded"
                >
                  Add Promo Code
                </button>
              </div>
            </form>
          </div>
        </div>
      )}

      {/* Edit Promo Code Modal */}
      {showEditModal && currentPromoCode && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full flex items-center justify-center">
          <div className="relative bg-white rounded-lg shadow-xl max-w-md w-full">
            <div className="flex justify-between items-center p-5 border-b">
              <h3 className="text-lg font-semibold">Edit Promo Code</h3>
              <button 
                onClick={() => setShowEditModal(false)}
                className="text-gray-400 hover:text-gray-500"
              >
                <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>
            <form onSubmit={handleSubmitEdit} className="p-5 max-h-[80vh] overflow-y-auto">
              {/* Form fields will go here */}
              <div className="flex justify-end">
                <button
                  type="button"
                  onClick={() => setShowEditModal(false)}
                  className="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded mr-2"
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  className="bg-primary-600 hover:bg-primary-700 text-white font-bold py-2 px-4 rounded"
                >
                  Update Promo Code
                </button>
              </div>
            </form>
          </div>
        </div>
      )}
    </div>
  );
};

export default PromoCodeManagement;