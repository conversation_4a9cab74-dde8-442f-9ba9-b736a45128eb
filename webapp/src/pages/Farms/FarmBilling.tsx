import { useState, useEffect, useContext } from 'react';
import { useNavigate, useParams, useLocation, Link } from 'react-router-dom';
import axios from 'axios';
import { AuthContext } from '../../context/AuthContext';
import Layout from '../../components/Layout';
import CustomDomainManager from '../../components/CustomDomainManager';
import CustomerPortalManager from '../../components/CustomerPortalManager';
import { API_URL } from '../../config';
import { 
import { getAuthToken } from '../utils/storageUtils';
  getPaymentMethods, 
  addPaymentMethod, 
  removePaymentMethod, 
  setDefaultPaymentMethod,
  PaymentMethod,
  PaymentProvider,
  initStripeSession, 
  SubscriptionOperation,
  cancelSubscription,
  getInvoices,
  Invoice
} from '../../services/paymentService';

interface SubscriptionPlan {
  id: string;
  name: string;
  description: string;
  price_monthly: number;
  price_yearly: number;
  features: any;
  max_farms: number;
  max_users: number;
  is_active: boolean;
}

interface SubscriptionTransaction {
  id: string;
  farm_id: string;
  subscription_plan_id: string;
  amount: number;
  currency: string;
  status: string;
  payment_method: string;
  payment_reference: string;
  transaction_date: string;
  billing_period_start: string;
  billing_period_end: string;
  SubscriptionPlan?: SubscriptionPlan;
}

interface Farm {
  id: string;
  name: string;
  billing_email: string;
  billing_address: string;
  billing_city: string;
  billing_state: string;
  billing_zip_code: string;
  billing_country: string;
  payment_method_id: string;
  stripe_customer_id: string;
  subscription_plan_id: string;
  subscription_status: string;
  subscription_start_date: string;
  subscription_end_date: string;
  SubscriptionPlan?: SubscriptionPlan;
}

const FarmBilling = () => {
  const { farmId } = useParams<{ farmId: string }>();
  const location = useLocation();
  const [farm, setFarm] = useState<Farm | null>(null);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [formData, setFormData] = useState({
    billing_email: '',
    billing_address: '',
    billing_city: '',
    billing_state: '',
    billing_zip_code: '',
    billing_country: 'USA',
  });
  const [paymentMethods, setPaymentMethods] = useState<PaymentMethod[]>([]);
  const [loadingPaymentMethods, setLoadingPaymentMethods] = useState(false);
  const [showAddPaymentModal, setShowAddPaymentModal] = useState(false);
  const [selectedProvider, setSelectedProvider] = useState<PaymentProvider>('stripe');
  const [processingPayment, setProcessingPayment] = useState(false);

  // Subscription-related state
  const [plans, setPlans] = useState<SubscriptionPlan[]>([]);
  const [transactions, setTransactions] = useState<SubscriptionTransaction[]>([]);
  const [invoices, setInvoices] = useState<Invoice[]>([]);
  const [billingCycle, setBillingCycle] = useState<'monthly' | 'yearly'>('monthly');
  const [showPaymentModal, setShowPaymentModal] = useState(false);
  const [selectedPlanId, setSelectedPlanId] = useState<string | null>(null);
  const [selectedOperation, setSelectedOperation] = useState<SubscriptionOperation>('subscribe');
  const [promoCode, setPromoCode] = useState<string>('');
  const [validatingPromoCode, setValidatingPromoCode] = useState(false);
  const [promoCodeValid, setPromoCodeValid] = useState<boolean | null>(null);
  const [promoCodeMessage, setPromoCodeMessage] = useState<string>('');
  const [showCancelConfirmation, setShowCancelConfirmation] = useState(false);
  const [cancelAtPeriodEnd, setCancelAtPeriodEnd] = useState(true);
  const [cancelingSubscription, setCancelingSubscription] = useState(false);

  // Get the tab from URL query parameter or default to 'billing'
  const queryParams = new URLSearchParams(location.search);
  const tabFromUrl = queryParams.get('tab');
  const isValidTab = (tab: string | null): tab is 'billing' | 'subscription' | 'invoices' => {
    return tab === 'billing' || tab === 'subscription' || tab === 'invoices';
  };
  const [activeTab, setActiveTab] = useState<'billing' | 'subscription' | 'invoices'>(
    isValidTab(tabFromUrl) ? tabFromUrl : 'billing'
  );

  const [filterStatus, setFilterStatus] = useState<string>('');
  const [filterDateFrom, setFilterDateFrom] = useState<string>('');
  const [filterDateTo, setFilterDateTo] = useState<string>('');
  const [filteredInvoices, setFilteredInvoices] = useState<Invoice[]>([]);
  const [filteredTransactions, setFilteredTransactions] = useState<SubscriptionTransaction[]>([]);

  const { user } = useContext(AuthContext);
  const navigate = useNavigate();

  // Effect to update activeTab when URL changes
  useEffect(() => {
    const queryParams = new URLSearchParams(location.search);
    const tabFromUrl = queryParams.get('tab');
    if (isValidTab(tabFromUrl) && tabFromUrl !== activeTab) {
      setActiveTab(tabFromUrl);
    }
  }, [location.search, activeTab]);

  useEffect(() => {
    // Check if user is authenticated
    if (!user) {
      navigate('/login');
      return;
    }

    const fetchData = async () => {
      try {
        setLoading(true);
        setError(null);

        // Fetch farm data
        const response = await axios.get(`${API_URL}/farms/${farmId}`, {
          headers: {
            Authorization: `Bearer ${getAuthToken()}`
          }
        });

        const farmData = response.data.farm;
        setFarm(farmData);

        // Initialize form data with farm billing info
        setFormData({
          billing_email: farmData.billing_email || '',
          billing_address: farmData.billing_address || '',
          billing_city: farmData.billing_city || '',
          billing_state: farmData.billing_state || '',
          billing_zip_code: farmData.billing_zip_code || '',
          billing_country: farmData.billing_country || 'USA',
        });

        // Fetch payment methods
        setLoadingPaymentMethods(true);
        try {
          const methods = await getPaymentMethods(farmId);
          setPaymentMethods(methods);
        } catch (paymentErr) {
          console.error('Error fetching payment methods:', paymentErr);
          // Don't fail the whole request if payment methods can't be fetched
        } finally {
          setLoadingPaymentMethods(false);
        }

        // Fetch subscription plans
        try {
          const plansResponse = await axios.get(`${API_URL}/subscriptions/plans`, {
            headers: {
              Authorization: `Bearer ${getAuthToken()}`
            }
          });
          setPlans(plansResponse.data.plans || []);
        } catch (plansErr) {
          console.error('Error fetching subscription plans:', plansErr);
          // Don't fail the whole request if plans can't be fetched
        }

        // Fetch subscription transactions
        try {
          const transactionsResponse = await axios.get(`${API_URL}/subscriptions/transactions/farm/${farmId}`, {
            headers: {
              Authorization: `Bearer ${getAuthToken()}`
            }
          });
          const fetchedTransactions = transactionsResponse.data.transactions || [];
          setTransactions(fetchedTransactions);
          setFilteredTransactions(fetchedTransactions);
        } catch (transactionsErr) {
          console.error('Error fetching subscription transactions:', transactionsErr);
          // Don't fail the whole request if transactions can't be fetched
        }

        // Fetch invoices
        try {
          const fetchedInvoices = await getInvoices(farmId);
          setInvoices(fetchedInvoices);
          setFilteredInvoices(fetchedInvoices);
        } catch (invoiceErr) {
          console.error('Error fetching invoices:', invoiceErr);
          // Don't fail the whole request if invoices can't be fetched
        }
      } catch (err: any) {
        console.error('Error fetching farm:', err);
        setError(err.response?.data?.error || 'Failed to load farm details');
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [user, navigate, farmId]);

  // Apply filters to invoices and transactions
  const applyFilters = () => {
    // Filter invoices
    let filtered = [...invoices];

    if (filterStatus) {
      filtered = filtered.filter(invoice => invoice.status === filterStatus);
    }

    if (filterDateFrom) {
      const fromDate = new Date(filterDateFrom);
      filtered = filtered.filter(invoice => new Date(invoice.date) >= fromDate);
    }

    if (filterDateTo) {
      const toDate = new Date(filterDateTo);
      // Set time to end of day
      toDate.setHours(23, 59, 59, 999);
      filtered = filtered.filter(invoice => new Date(invoice.date) <= toDate);
    }

    setFilteredInvoices(filtered);

    // Filter transactions
    let filteredTrans = [...transactions];

    if (filterStatus) {
      // Map status values to transaction status values
      const statusMap: Record<string, string> = {
        'paid': 'successful',
        'pending': 'pending',
        'failed': 'failed'
      };
      filteredTrans = filteredTrans.filter(transaction => transaction.status === statusMap[filterStatus] || (!statusMap[filterStatus] && transaction.status === filterStatus));
    }

    if (filterDateFrom) {
      const fromDate = new Date(filterDateFrom);
      filteredTrans = filteredTrans.filter(transaction => new Date(transaction.transaction_date) >= fromDate);
    }

    if (filterDateTo) {
      const toDate = new Date(filterDateTo);
      // Set time to end of day
      toDate.setHours(23, 59, 59, 999);
      filteredTrans = filteredTrans.filter(transaction => new Date(transaction.transaction_date) <= toDate);
    }

    setFilteredTransactions(filteredTrans);
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    try {
      setSaving(true);
      setError(null);

      // Update farm billing information
      await axios.put(`${API_URL}/farms/${farmId}`, formData, {
        headers: {
          Authorization: `Bearer ${getAuthToken()}`
        }
      });

      // Navigate back to farm detail page
      navigate(`/farms/${farmId}`);
    } catch (err: any) {
      console.error('Error updating billing information:', err);
      setError(err.response?.data?.error || 'Failed to update billing information');
      setSaving(false);
    }
  };

  // Function to open the add payment method modal
  const handleAddPaymentMethod = () => {
    setShowAddPaymentModal(true);
  };

  // Function to initiate the payment method addition process
  const handleInitiateAddPaymentMethod = async (provider: PaymentProvider) => {
    if (!farmId) return;

    try {
      setProcessingPayment(true);
      setSelectedProvider(provider);

      // In a real implementation, this would redirect to Stripe to collect payment details
      // For now, we'll simulate the process with a mock payment method ID
      const mockPaymentMethodId = `mock_${provider}_${Date.now()}`;

      // Add the payment method
      await handleCompleteAddPaymentMethod(mockPaymentMethodId, provider);

    } catch (err: any) {
      console.error('Error initiating payment method addition:', err);
      setError(err.response?.data?.error || 'Failed to add payment method');
      setProcessingPayment(false);
    }
  };

  // Function to complete the payment method addition process
  const handleCompleteAddPaymentMethod = async (paymentMethodId: string, provider: PaymentProvider) => {
    if (!farmId) return;

    try {
      // Add the payment method using the payment service
      await addPaymentMethod(farmId, paymentMethodId, provider, true);

      // Refresh payment methods
      const methods = await getPaymentMethods(farmId);
      setPaymentMethods(methods);

      setShowAddPaymentModal(false);
      setProcessingPayment(false);

      // Show success message
      alert('Payment method added successfully.');
    } catch (err: any) {
      console.error('Error adding payment method:', err);
      setError(err.response?.data?.error || 'Failed to add payment method');
      setProcessingPayment(false);
    }
  };

  // Function to remove a payment method
  const handleRemovePaymentMethod = async (paymentMethodId: string) => {
    if (!farmId) return;

    try {
      // Confirm with the user
      const confirmed = window.confirm('Are you sure you want to remove this payment method?');
      if (!confirmed) return;

      // Remove the payment method using the payment service
      await removePaymentMethod(farmId, paymentMethodId);

      // Refresh payment methods
      const methods = await getPaymentMethods(farmId);
      setPaymentMethods(methods);

      // Show success message
      alert('Payment method removed successfully.');
    } catch (err: any) {
      console.error('Error removing payment method:', err);
      setError(err.response?.data?.error || 'Failed to remove payment method');
    }
  };

  // Function to set a payment method as default
  const handleSetDefaultPaymentMethod = async (paymentMethodId: string) => {
    if (!farmId) return;

    try {
      // Set the payment method as default using the payment service
      await setDefaultPaymentMethod(farmId, paymentMethodId);

      // Refresh payment methods
      const methods = await getPaymentMethods(farmId);
      setPaymentMethods(methods);

      // Show success message
      alert('Default payment method updated successfully.');
    } catch (err: any) {
      console.error('Error setting default payment method:', err);
      setError(err.response?.data?.error || 'Failed to set default payment method');
    }
  };

  // Function to handle subscription
  const handleSubscribe = (planId: string) => {
    // Determine if this is a new subscription or an upgrade/downgrade
    const operation: SubscriptionOperation = farm?.subscription_plan_id 
      ? (farm.subscription_plan_id !== planId ? 'upgrade' : 'subscribe')
      : 'subscribe';

    setSelectedPlanId(planId);
    setSelectedOperation(operation);
    setShowPaymentModal(true);
  };

  // Function to validate promo code
  const validatePromoCode = async () => {
    if (!promoCode.trim() || !selectedPlanId || !farmId) return;

    try {
      setValidatingPromoCode(true);
      setPromoCodeValid(null);
      setPromoCodeMessage('');

      const response = await axios.post(
        `${API_URL}/subscriptions/promo-codes/validate`,
        {
          code: promoCode,
          subscriptionPlanId: selectedPlanId,
          userId: user?.id,
          farmId: farmId,
          billingCycle
        },
        {
          headers: {
            Authorization: `Bearer ${getAuthToken()}`
          }
        }
      );

      if (response.data && response.data.message) {
        setPromoCodeValid(true);
        setPromoCodeMessage(response.data.message);
      }
    } catch (err: any) {
      console.error('Error validating promo code:', err);
      setPromoCodeValid(false);
      setPromoCodeMessage(err.response?.data?.error || 'Invalid promo code');
    } finally {
      setValidatingPromoCode(false);
    }
  };

  // Function to handle payment method selection for subscription
  const handlePaymentMethodSelect = async (provider: PaymentProvider) => {
    if (!selectedPlanId || !farmId) return;

    try {
      setProcessingPayment(true);
      setError(null);

      let sessionData;
      const promoCodeToApply = promoCodeValid ? promoCode : null;

      // Initialize payment session with Stripe
      sessionData = await initStripeSession(
        selectedPlanId,
        farmId,
        selectedOperation,
        billingCycle,
        promoCodeToApply
      );

      // Redirect to Stripe Checkout
      window.location.href = sessionData.url;

      // The user will be redirected to the payment provider's checkout page,
      // and then back to our success or cancel page
    } catch (err: any) {
      console.error('Error processing payment:', err);
      setError(err.response?.data?.error || 'Failed to process payment');
      setProcessingPayment(false);
      setShowPaymentModal(false);
    }
  };

  // Function to handle cancel subscription
  const handleCancelSubscription = () => {
    setShowCancelConfirmation(true);
  };

  // Function to confirm cancel subscription
  const confirmCancelSubscription = async () => {
    try {
      if (!farmId) return;

      setCancelingSubscription(true);
      setError(null);

      // Call the cancelSubscription function from the payment service
      await cancelSubscription(farmId, cancelAtPeriodEnd);

      // Refresh farm data
      const farmResponse = await axios.get(`${API_URL}/farms/${farmId}`, {
        headers: {
          Authorization: `Bearer ${getAuthToken()}`
        }
      });

      setFarm(farmResponse.data.farm || null);
      setShowCancelConfirmation(false);
      setCancelingSubscription(false);

      // Show success message
      alert(cancelAtPeriodEnd 
        ? 'Your subscription will be canceled at the end of the current billing period.' 
        : 'Your subscription has been canceled immediately.');
    } catch (err: any) {
      console.error('Error canceling subscription:', err);
      setError(err.response?.data?.error || 'Failed to cancel subscription');
      setCancelingSubscription(false);
    }
  };

  // Utility function to format price
  const formatPrice = (price: number, currency = 'USD') => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency
    }).format(price);
  };

  // Utility function to format date
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  return (
    <Layout>
      {loading ? (
        <div className="p-8 text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600 mx-auto"></div>
          <p className="mt-2 text-gray-500">Loading farm details...</p>
        </div>
      ) : error ? (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mb-6" role="alert">
          <span className="block sm:inline">{error}</span>
        </div>
      ) : farm ? (
        <>
          <div className="flex justify-between items-center mb-6">
            <h1 className="text-2xl font-bold text-gray-900">Billing & Subscription for {farm.name}</h1>
            <Link
              to={`/farms/${farm.id}`}
              className="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md shadow-sm text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
            >
              Back to Farm
            </Link>
          </div>

          {/* Tabs */}
          <div className="border-b border-gray-200 mb-6">
            <nav className="-mb-px flex space-x-8">
              <button
                onClick={() => {
                  setActiveTab('billing');
                  navigate(`/farms/${farmId}/billing?tab=billing`);
                }}
                className={`${
                  activeTab === 'billing'
                    ? 'border-primary-500 text-primary-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                } whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm`}
              >
                Billing Information
              </button>
              <button
                onClick={() => {
                  setActiveTab('subscription');
                  navigate(`/farms/${farmId}/billing?tab=subscription`);
                }}
                className={`${
                  activeTab === 'subscription'
                    ? 'border-primary-500 text-primary-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                } whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm`}
              >
                Subscription Management
              </button>
              <button
                onClick={() => {
                  setActiveTab('invoices');
                  navigate(`/farms/${farmId}/billing?tab=invoices`);
                }}
                className={`${
                  activeTab === 'invoices'
                    ? 'border-primary-500 text-primary-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                } whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm`}
              >
                Invoices & Payment History
              </button>
            </nav>
          </div>

          {activeTab === 'billing' && (
            <div className="bg-white shadow rounded-lg overflow-hidden mb-6">
              <div className="px-6 py-4 border-b border-gray-200 bg-gray-50">
                <h2 className="text-lg font-medium text-gray-900">Billing Address</h2>
              </div>
              <div className="p-6">
                <form onSubmit={handleSubmit}>
                  <div className="grid grid-cols-1 gap-y-6 gap-x-4 sm:grid-cols-6">
                    <div className="sm:col-span-6">
                      <label htmlFor="billing_email" className="block text-sm font-medium text-gray-700">
                        Billing Email
                      </label>
                      <div className="mt-1">
                        <input
                          type="email"
                          name="billing_email"
                          id="billing_email"
                          value={formData.billing_email}
                          onChange={handleInputChange}
                          className="shadow-sm focus:ring-primary-500 focus:border-primary-500 block w-full sm:text-sm border-gray-300 rounded-md"
                        />
                      </div>
                    </div>

                    <div className="sm:col-span-6">
                      <label htmlFor="billing_address" className="block text-sm font-medium text-gray-700">
                        Street Address
                      </label>
                      <div className="mt-1">
                        <input
                          type="text"
                          name="billing_address"
                          id="billing_address"
                          value={formData.billing_address}
                          onChange={handleInputChange}
                          className="shadow-sm focus:ring-primary-500 focus:border-primary-500 block w-full sm:text-sm border-gray-300 rounded-md"
                        />
                      </div>
                    </div>

                    <div className="sm:col-span-2">
                      <label htmlFor="billing_city" className="block text-sm font-medium text-gray-700">
                        City
                      </label>
                      <div className="mt-1">
                        <input
                          type="text"
                          name="billing_city"
                          id="billing_city"
                          value={formData.billing_city}
                          onChange={handleInputChange}
                          className="shadow-sm focus:ring-primary-500 focus:border-primary-500 block w-full sm:text-sm border-gray-300 rounded-md"
                        />
                      </div>
                    </div>

                    <div className="sm:col-span-2">
                      <label htmlFor="billing_state" className="block text-sm font-medium text-gray-700">
                        State / Province
                      </label>
                      <div className="mt-1">
                        <input
                          type="text"
                          name="billing_state"
                          id="billing_state"
                          value={formData.billing_state}
                          onChange={handleInputChange}
                          className="shadow-sm focus:ring-primary-500 focus:border-primary-500 block w-full sm:text-sm border-gray-300 rounded-md"
                        />
                      </div>
                    </div>

                    <div className="sm:col-span-2">
                      <label htmlFor="billing_zip_code" className="block text-sm font-medium text-gray-700">
                        ZIP / Postal Code
                      </label>
                      <div className="mt-1">
                        <input
                          type="text"
                          name="billing_zip_code"
                          id="billing_zip_code"
                          value={formData.billing_zip_code}
                          onChange={handleInputChange}
                          className="shadow-sm focus:ring-primary-500 focus:border-primary-500 block w-full sm:text-sm border-gray-300 rounded-md"
                        />
                      </div>
                    </div>

                    <div className="sm:col-span-3">
                      <label htmlFor="billing_country" className="block text-sm font-medium text-gray-700">
                        Country
                      </label>
                      <div className="mt-1">
                        <select
                          id="billing_country"
                          name="billing_country"
                          value={formData.billing_country}
                          onChange={handleInputChange}
                          className="shadow-sm focus:ring-primary-500 focus:border-primary-500 block w-full sm:text-sm border-gray-300 rounded-md"
                        >
                          <option value="USA">United States</option>
                          <option value="CAN">Canada</option>
                          <option value="MEX">Mexico</option>
                          {/* Add more countries as needed */}
                        </select>
                      </div>
                    </div>
                  </div>

                  <div className="mt-6 flex justify-end">
                    <button
                      type="button"
                      onClick={() => navigate(`/farms/${farm.id}`)}
                      className="bg-white py-2 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 mr-3"
                    >
                      Cancel
                    </button>
                    <button
                      type="submit"
                      disabled={saving}
                      className={`inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 ${saving ? 'opacity-70 cursor-not-allowed' : ''}`}
                    >
                      {saving ? 'Saving...' : 'Save Billing Information'}
                    </button>
                  </div>
                </form>
              </div>
            </div>
          )}

          {(activeTab === 'billing' || activeTab === 'subscription') && (
            <div className="bg-white shadow rounded-lg overflow-hidden mb-6">
              <div className="px-6 py-4 border-b border-gray-200 bg-gray-50 flex justify-between items-center">
                <h2 className="text-lg font-medium text-gray-900">Payment Methods</h2>
                <button
                  type="button"
                  onClick={handleAddPaymentMethod}
                  className="inline-flex items-center px-3 py-1 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
                >
                  Add New
                </button>
              </div>
              <div className="p-6">
                {loadingPaymentMethods ? (
                  <div className="flex justify-center items-center py-4">
                    <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary-600"></div>
                    <span className="ml-2 text-sm text-gray-500">Loading payment methods...</span>
                  </div>
                ) : paymentMethods.length > 0 ? (
                  <div className="space-y-4">
                    {paymentMethods.map((method) => (
                      <div key={method.id} className="flex items-center justify-between border-b pb-4 last:border-b-0 last:pb-0">
                        <div className="flex items-center">
                          {method.type === 'card' ? (
                            <svg className="h-8 w-8 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z" />
                            </svg>
                          ) : (
                            <svg className="h-8 w-8 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z" />
                            </svg>
                          )}
                          <div className="ml-4">
                            <div className="flex items-center">
                              <p className="text-sm font-medium text-gray-900">
                                {method.type === 'card' 
                                  ? `${method.brand || 'Card'} ending in ${method.last4 || '****'}`
                                  : 'Bank Account'}
                              </p>
                              {method.isDefault && (
                                <span className="ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                  Default
                                </span>
                              )}
                            </div>
                            {method.type === 'card' && method.expMonth && method.expYear && (
                              <p className="text-sm text-gray-500">Expires {method.expMonth}/{method.expYear}</p>
                            )}
                          </div>
                        </div>
                        <div className="flex space-x-2">
                          {!method.isDefault && (
                            <button
                              type="button"
                              onClick={() => handleSetDefaultPaymentMethod(method.id)}
                              className="text-sm text-primary-600 hover:text-primary-900"
                            >
                              Set as Default
                            </button>
                          )}
                          <button
                            type="button"
                            onClick={() => handleRemovePaymentMethod(method.id)}
                            className="text-sm text-red-600 hover:text-red-900"
                          >
                            Remove
                          </button>
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-6">
                    <svg className="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z" />
                    </svg>
                    <h3 className="mt-2 text-sm font-medium text-gray-900">No payment methods</h3>
                    <p className="mt-1 text-sm text-gray-500">Add a payment method to manage your subscription.</p>
                    <div className="mt-6">
                      <button
                        type="button"
                        onClick={handleAddPaymentMethod}
                        className="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
                      >
                        Add Payment Method
                      </button>
                    </div>
                  </div>
                )}
              </div>
            </div>
          )}

          {activeTab === 'subscription' && (
            <>
              <div className="bg-white shadow rounded-lg overflow-hidden mb-6">
                <div className="px-6 py-4 border-b border-gray-200 bg-gray-50">
                  <h2 className="text-lg font-medium text-gray-900">Custom Domain</h2>
                </div>
                <div className="p-6">
                  {farm && farm.SubscriptionPlan && (
                    <CustomDomainManager 
                      farmId={farm.id} 
                      featureEnabled={farm.SubscriptionPlan.features?.custom_domain_enabled === true}
                    />
                  )}
                </div>
              </div>

              <div className="bg-white shadow rounded-lg overflow-hidden mb-6">
                <div className="px-6 py-4 border-b border-gray-200 bg-gray-50">
                  <h2 className="text-lg font-medium text-gray-900">Customer Portal</h2>
                </div>
                <div className="p-6">
                  {farm && farm.SubscriptionPlan && (
                    <CustomerPortalManager 
                      farmId={farm.id} 
                      featureEnabled={farm.SubscriptionPlan.features?.customer_portal_enabled === true}
                    />
                  )}
                </div>
              </div>
            </>
          )}

          {activeTab === 'invoices' && (
            <div className="space-y-6">
              {/* Filter Controls */}
              <div className="bg-white shadow rounded-lg overflow-hidden">
                <div className="px-6 py-4 border-b border-gray-200 bg-gray-50">
                  <h2 className="text-lg font-medium text-gray-900">Filter Options</h2>
                </div>
                <div className="p-6">
                  <div className="grid grid-cols-1 gap-y-6 gap-x-4 sm:grid-cols-6">
                    <div className="sm:col-span-2">
                      <label htmlFor="filter-status" className="block text-sm font-medium text-gray-700">
                        Status
                      </label>
                      <div className="mt-1">
                        <select
                          id="filter-status"
                          name="filter-status"
                          value={filterStatus}
                          onChange={(e) => setFilterStatus(e.target.value)}
                          className="shadow-sm focus:ring-primary-500 focus:border-primary-500 block w-full sm:text-sm border-gray-300 rounded-md"
                        >
                          <option value="">All Statuses</option>
                          <option value="paid">Paid</option>
                          <option value="pending">Pending</option>
                          <option value="failed">Failed</option>
                        </select>
                      </div>
                    </div>
                    <div className="sm:col-span-2">
                      <label htmlFor="filter-date-from" className="block text-sm font-medium text-gray-700">
                        From Date
                      </label>
                      <div className="mt-1">
                        <input
                          type="date"
                          name="filter-date-from"
                          id="filter-date-from"
                          value={filterDateFrom}
                          onChange={(e) => setFilterDateFrom(e.target.value)}
                          className="shadow-sm focus:ring-primary-500 focus:border-primary-500 block w-full sm:text-sm border-gray-300 rounded-md"
                        />
                      </div>
                    </div>
                    <div className="sm:col-span-2">
                      <label htmlFor="filter-date-to" className="block text-sm font-medium text-gray-700">
                        To Date
                      </label>
                      <div className="mt-1">
                        <input
                          type="date"
                          name="filter-date-to"
                          id="filter-date-to"
                          value={filterDateTo}
                          onChange={(e) => setFilterDateTo(e.target.value)}
                          className="shadow-sm focus:ring-primary-500 focus:border-primary-500 block w-full sm:text-sm border-gray-300 rounded-md"
                        />
                      </div>
                    </div>
                  </div>
                  <div className="mt-4 flex justify-end space-x-3">
                    <button
                      type="button"
                      onClick={() => {
                        setFilterStatus('');
                        setFilterDateFrom('');
                        setFilterDateTo('');
                        setFilteredInvoices(invoices);
                        setFilteredTransactions(transactions);
                      }}
                      className="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md shadow-sm text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
                    >
                      Reset Filters
                    </button>
                    <button
                      type="button"
                      onClick={applyFilters}
                      className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
                    >
                      Apply Filters
                    </button>
                  </div>
                </div>
              </div>

              {/* Upcoming Invoices */}
              <div className="bg-white shadow rounded-lg overflow-hidden">
                <div className="px-6 py-4 border-b border-gray-200 bg-gray-50">
                  <h2 className="text-lg font-medium text-gray-900">Upcoming Invoices</h2>
                </div>
                <div className="p-6">
                  {farm.subscription_status === 'active' ? (
                    <div className="bg-white rounded-lg border border-gray-200 overflow-hidden">
                      <div className="px-4 py-5 sm:p-6">
                        <div className="flex items-center justify-between">
                          <div>
                            <h3 className="text-lg leading-6 font-medium text-gray-900">Next Billing Cycle</h3>
                            <div className="mt-2 max-w-xl text-sm text-gray-500">
                              <p>Your next invoice will be generated on {farm.subscription_end_date ? new Date(farm.subscription_end_date).toLocaleDateString() : 'N/A'}</p>
                            </div>
                            <div className="mt-3 text-sm">
                              <span className="font-medium text-gray-900">
                                {farm.SubscriptionPlan ? `$${farm.SubscriptionPlan.price_monthly.toFixed(2)}` : 'N/A'}
                              </span>
                              <span className="text-gray-500"> / month</span>
                            </div>
                          </div>
                          <div className="ml-4 flex-shrink-0">
                            <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                              Upcoming
                            </span>
                          </div>
                        </div>
                      </div>
                    </div>
                  ) : (
                    <div className="text-center py-6">
                      <svg className="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                      </svg>
                      <h3 className="mt-2 text-sm font-medium text-gray-900">No upcoming invoices</h3>
                      <p className="mt-1 text-sm text-gray-500">
                        {farm.subscription_status === 'canceled' 
                          ? 'Your subscription has been canceled.' 
                          : 'You don\'t have an active subscription.'}
                      </p>
                    </div>
                  )}
                </div>
              </div>

              {/* Previous Invoices */}
              <div className="bg-white shadow rounded-lg overflow-hidden">
                <div className="px-6 py-4 border-b border-gray-200 bg-gray-50">
                  <h2 className="text-lg font-medium text-gray-900">Previous Invoices</h2>
                </div>
                <div className="p-6">
                  {filteredInvoices.length > 0 ? (
                    <div className="overflow-x-auto">
                      <table className="min-w-full divide-y divide-gray-200">
                        <thead className="bg-gray-50">
                          <tr>
                            <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                              Invoice Date
                            </th>
                            <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                              Amount
                            </th>
                            <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                              Status
                            </th>
                            <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                              Actions
                            </th>
                          </tr>
                        </thead>
                        <tbody className="bg-white divide-y divide-gray-200">
                          {filteredInvoices.map((invoice) => (
                            <tr key={invoice.id}>
                              <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                {new Date(invoice.date).toLocaleDateString()}
                              </td>
                              <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                {formatPrice(invoice.amount, invoice.currency)}
                              </td>
                              <td className="px-6 py-4 whitespace-nowrap">
                                <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                                  invoice.status === 'paid' 
                                    ? 'bg-green-100 text-green-800' 
                                    : invoice.status === 'pending'
                                    ? 'bg-yellow-100 text-yellow-800'
                                    : 'bg-red-100 text-red-800'
                                }`}>
                                  {invoice.status.charAt(0).toUpperCase() + invoice.status.slice(1)}
                                </span>
                              </td>
                              <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                {invoice.pdfUrl && (
                                  <a 
                                    href={invoice.pdfUrl} 
                                    target="_blank" 
                                    rel="noopener noreferrer"
                                    className="text-primary-600 hover:text-primary-900"
                                  >
                                    Download PDF
                                  </a>
                                )}
                              </td>
                            </tr>
                          ))}
                        </tbody>
                      </table>
                    </div>
                  ) : (
                    <div className="text-center py-6">
                      <svg className="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                      </svg>
                      <h3 className="mt-2 text-sm font-medium text-gray-900">No previous invoices</h3>
                      <p className="mt-1 text-sm text-gray-500">You don't have any previous invoices.</p>
                    </div>
                  )}
                </div>
              </div>

              {/* Payment History */}
              <div className="bg-white shadow rounded-lg overflow-hidden">
                <div className="px-6 py-4 border-b border-gray-200 bg-gray-50">
                  <h2 className="text-lg font-medium text-gray-900">Payment History</h2>
                </div>
                <div className="p-6">
                  {filteredTransactions.length > 0 ? (
                    <div className="overflow-x-auto">
                      <table className="min-w-full divide-y divide-gray-200">
                        <thead className="bg-gray-50">
                          <tr>
                            <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                              Date
                            </th>
                            <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                              Amount
                            </th>
                            <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                              Status
                            </th>
                            <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                              Payment Method
                            </th>
                          </tr>
                        </thead>
                        <tbody className="bg-white divide-y divide-gray-200">
                          {filteredTransactions.map((transaction) => (
                            <tr key={transaction.id}>
                              <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                {formatDate(transaction.transaction_date)}
                              </td>
                              <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                {formatPrice(transaction.amount, transaction.currency)}
                              </td>
                              <td className="px-6 py-4 whitespace-nowrap">
                                <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                                  transaction.status === 'successful' 
                                    ? 'bg-green-100 text-green-800' 
                                    : transaction.status === 'pending'
                                    ? 'bg-yellow-100 text-yellow-800'
                                    : 'bg-red-100 text-red-800'
                                }`}>
                                  {transaction.status.charAt(0).toUpperCase() + transaction.status.slice(1)}
                                </span>
                              </td>
                              <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                {transaction.payment_method.charAt(0).toUpperCase() + transaction.payment_method.slice(1).replace('_', ' ')}
                              </td>
                            </tr>
                          ))}
                        </tbody>
                      </table>
                    </div>
                  ) : (
                    <div className="text-center py-6">
                      <svg className="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                      </svg>
                      <h3 className="mt-2 text-sm font-medium text-gray-900">No payment history</h3>
                      <p className="mt-1 text-sm text-gray-500">You don't have any payment history.</p>
                    </div>
                  )}
                </div>
              </div>
            </div>
          )}
        </>
      ) : (
        <div className="bg-white shadow rounded-lg p-8 text-center">
          <p className="text-gray-500">Farm not found.</p>
          <Link
            to="/farms"
            className="mt-4 inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
          >
            Back to Farms
          </Link>
        </div>
      )}

      {/* Add Payment Method Modal */}
      {showAddPaymentModal && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full flex items-center justify-center z-50">
          <div className="relative bg-white rounded-lg shadow-xl max-w-md w-full">
            <div className="flex justify-between items-center p-5 border-b">
              <h3 className="text-lg font-semibold">Add Payment Method</h3>
              <button 
                onClick={() => setShowAddPaymentModal(false)}
                disabled={processingPayment}
                className="text-gray-400 hover:text-gray-500"
              >
                <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>
            <div className="p-5">
              <p className="text-sm text-gray-500 mb-4">
                Choose your preferred payment method to add to your account.
              </p>

              <div className="space-y-4">
                <button
                  onClick={() => handleInitiateAddPaymentMethod('stripe')}
                  disabled={processingPayment}
                  className="w-full flex items-center justify-between p-4 border rounded-lg hover:bg-gray-50 transition-colors"
                >
                  <div className="flex items-center">
                    <svg className="h-8 w-8 text-blue-500" viewBox="0 0 24 24" fill="currentColor">
                      <path d="M14.24 0H14.12L7.55.02c-.18.01-.31.01-.4.02-.08 0-.16.01-.23.02-.08 0-.15.01-.22.02-.07.01-.14.02-.2.03-.07.01-.13.02-.19.04-.06.01-.12.03-.18.04-.06.02-.11.03-.16.05-.05.02-.1.04-.15.06-.05.02-.1.04-.14.06-.05.03-.09.05-.13.08-.04.02-.08.05-.12.08-.04.03-.08.06-.11.09-.04.03-.07.06-.1.1-.03.03-.06.07-.09.1-.03.04-.05.07-.08.11-.02.04-.05.08-.07.12-.02.04-.04.08-.06.12-.02.04-.03.09-.05.13-.01.04-.03.09-.04.14-.01.05-.02.09-.03.14-.01.05-.02.1-.02.15-.01.05-.01.1-.01.16 0 .05-.01.11-.01.16v16.12c0 .06 0 .11.01.17 0 .05 0 .1.01.15 0 .**********.***********.**********.***********.***********.***********.04.04.08.06.12.02.04.04.08.07.12.02.04.05.07.08.11.02.03.05.07.08.1.03.04.06.07.1.1.03.03.07.06.11.09.04.03.07.05.12.08.04.02.08.05.13.07.04.02.09.05.14.07.05.02.1.04.15.06.05.02.1.03.16.05.06.01.12.03.18.04.06.01.12.03.19.03.07.01.14.02.21.03.07 0 .15.01.23.01.09.01.22.01.4.02l6.97.02v-7.12c0-.11 0-.21.01-.31.01-.11.02-.21.04-.31.02-.1.04-.2.07-.29.03-.1.06-.19.1-.28.04-.09.08-.18.13-.26.05-.08.1-.16.16-.24.06-.08.12-.15.19-.22.07-.07.14-.14.22-.2.08-.06.16-.12.24-.17.08-.05.17-.1.26-.14.09-.04.18-.08.28-.11.1-.03.2-.06.3-.08.1-.02.2-.04.31-.05.11-.01.21-.02.32-.02h1.11c.11 0 .21.01.32.02.1.01.21.03.31.05.1.02.2.05.3.08.1.03.19.07.28.11.09.04.18.09.26.14.08.05.16.11.24.17.08.06.15.13.22.2.07.07.13.14.19.22.06.08.11.16.16.24.05.08.09.17.13.26.04.09.07.18.1.28.03.09.05.19.07.29.02.1.03.2.04.31.01.1.01.2.01.31v7.12h1.17c.09 0 .19 0 .28-.01.09-.01.18-.02.26-.03.09-.01.17-.03.25-.05.08-.02.16-.04.24-.07.08-.03.15-.06.22-.09.07-.03.14-.07.2-.11.07-.04.13-.08.19-.13.06-.04.12-.09.17-.14.05-.05.11-.1.16-.16.05-.05.09-.11.14-.17.04-.06.08-.12.12-.19.04-.06.07-.13.1-.2.03-.07.06-.14.08-.22.03-.07.05-.15.06-.23.02-.08.03-.16.04-.25.01-.08.02-.17.02-.26.01-.09.01-.18.01-.28V7.59c0-.09 0-.19-.01-.28-.01-.09-.01-.18-.02-.26-.01-.09-.02-.17-.04-.25-.02-.08-.04-.16-.06-.23-.03-.08-.05-.15-.08-.22-.03-.07-.06-.14-.1-.2-.04-.07-.08-.13-.12-.19-.04-.06-.09-.12-.14-.17-.05-.06-.1-.11-.16-.16-.05-.05-.11-.1-.17-.14-.06-.05-.12-.09-.19-.13-.06-.04-.13-.08-.2-.11-.07-.03-.14-.06-.22-.09-.08-.03-.16-.05-.24-.07-.08-.02-.16-.04-.25-.05-.09-.01-.17-.02-.26-.03-.09-.01-.19-.01-.28-.01h-1.17v.02H14.24z"/>
                    </svg>
                    <span className="ml-3 font-medium">Add Credit Card</span>
                  </div>
                  <svg className="h-5 w-5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 5l7 7-7 7" />
                  </svg>
                </button>

              </div>

              {processingPayment && (
                <div className="mt-4 flex items-center justify-center">
                  <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary-600"></div>
                  <span className="ml-2 text-sm text-gray-500">Processing payment method...</span>
                </div>
              )}
            </div>
          </div>
        </div>
      )}
    </Layout>
  );
};

export default FarmBilling;
